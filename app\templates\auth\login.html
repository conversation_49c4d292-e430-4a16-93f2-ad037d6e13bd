{% extends "base.html" %}

{% block title %}Connexion - PromptAchat{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h2 class="card-title text-center mb-4">Connexion</h2>
                    
                    {# Ce div sera la cible pour les mises à jour HTMX du formulaire et des erreurs #}
                    <div id="login-form-content">
                        {% if not ldap_enabled %}
                            <div class="alert alert-info" role="alert">
                                L'authentification LDAP est actuellement désactivée. Vous pouvez vous connecter avec n'importe quelles informations pour accéder en tant que développeur.
                            </div>
                        {% endif %}
                        
                        <div id="login-error-message" class="mb-3">
                            {% if error_message %}
                                <div class="alert alert-danger" role="alert">
                                    {{ error_message }}
                                </div>
                            {% endif %}
                            {% if success_message %}
                                <div class="alert alert-success" role="alert">
                                    {{ success_message }}
                                </div>
                            {% endif %}
                        </div>

                        <form 
                            action="{{ url_for('login_submit') }}" 
                            method="post"
                            id="login-form">
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur</label>
                                <input type="text" class="form-control" id="username" name="username" value="{{ username_submitted or '' }}" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Mot de passe</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            
                            {% if return_to %}
                            <input type="hidden" name="return_to" value="{{ return_to }}">
                            {% endif %}
                            
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                    </div> {# Fin de #login-form-content #}

                </div>
            </div>
            <p class="text-center mt-3">
                <small class="text-muted">Problèmes de connexion ? Contactez <a href="mailto:{{ config.get_access_contact_email() }}">{{ config.get_access_contact_email() }}</a></small>
            </p>
        </div>
    </div>
</div>

{% endblock %} 