<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}PromptAchat FastAPI{% endblock %}</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <!-- Styles personnalisés -->
    <link rel="stylesheet" href="{{ url_for('static', path='/css/style.css') }}">
    <!-- HTMX - Suppression de l'attribut integrity qui cause des problèmes -->
    <script src="https://unpkg.com/htmx.org@1.9.6"></script>
    
    {% block head %}{% endblock %}
    
    {% if session_id %}
    <script>
    // Script pour maintenir le session_id dans les liens
    document.addEventListener('DOMContentLoaded', function() {
        // Récupérer le session_id depuis l'URL
        const sessionId = "{{ session_id }}";
        
        // Fonction pour ajouter le session_id aux liens
        function addSessionToLinks() {
            const links = document.querySelectorAll('a');
            links.forEach(link => {
                // Ne pas modifier les liens externes ou les liens d'ancre
                if (link.href.startsWith(window.location.origin) && !link.href.includes('#')) {
                    // Vérifier si le lien a déjà des paramètres
                    if (link.href.includes('?')) {
                        if (!link.href.includes('session_id=')) {
                            link.href += '&session_id=' + sessionId;
                        }
                    } else {
                        link.href += '?session_id=' + sessionId;
                    }
                }
            });
        }
        
        // Ajouter le session_id aux liens existants
        addSessionToLinks();
        
        // Observer les modifications du DOM pour ajouter le session_id aux nouveaux liens
        const observer = new MutationObserver(function(mutations) {
            addSessionToLinks();
        });
        
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
        
        // Ajouter le session_id aux formulaires
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            // Ne pas modifier les formulaires qui ciblent d'autres domaines
            if (!form.action || form.action.startsWith(window.location.origin)) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'session_id';
                input.value = sessionId;
                form.appendChild(input);
            }
        });
    });
    </script>
    {% endif %}
</head>
<body>
    <header>
        {% block header %}{% endblock %}
    </header>
    <main>
        {% block content %}{% endblock %}
    </main>
    <footer>
        {% block footer %}{% endblock %}
    </footer>
    
    <!-- Bootstrap 5 JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 