{% extends "base.html" %}

{% block title %}Redirection - PromptAchat{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4 text-center">
                    <h2 class="card-title text-center mb-4">{{ message }}</h2>
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-3">Vous allez être redirigé automatiquement...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Utiliser document.cookie pour vérifier si le cookie est défini
    console.log("Cookies actuels:", document.cookie);
    
    // Utiliser un délai plus long pour s'assurer que le cookie est bien traité
    setTimeout(function() {
        // Utiliser document.location pour préserver les cookies
        document.location.replace("{{ redirect_url }}");
    }, 2000);
</script>

{% endblock %} 