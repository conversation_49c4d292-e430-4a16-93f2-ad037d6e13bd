# PromptAchat FastAPI

Ceci est la version FastAPI de l'application PromptAchat.

## Prérequis

- Python 3.9+ (ou la version spécifiée dans `pyproject.toml`)
- [Poetry](https://python-poetry.org/docs/#installation) (ou PDM, ou un autre gestionnaire de paquets si vous n'utilisez pas Poetry)

## Installation

1.  <PERSON><PERSON><PERSON> le dépôt (si ce n'est pas déjà fait).
2.  Naviguez vers le répertoire `promptachat_fastapi`:
    ```bash
    cd promptachat_fastapi
    ```
3.  Installez les dépendances en utilisant Poetry:
    ```bash
    poetry install
    ```
    (Si vous utilisez PDM, ce serait `pdm install`. Si vous avez opté pour `requirements_fastapi.txt`, créez un environnement virtuel et faites `pip install -r requirements_fastapi.txt`)

4.  <PERSON><PERSON><PERSON> `config.ini.template` vers `config.ini` (si ce n'est pas déjà fait par le script d'initialisation) et remplissez les valeurs nécessaires, notamment les clés API.
    ```bash
    # cp config.ini.template config.ini # (Si pas déjà présent)
    ```
    Assurez-vous que `prompts.json` est également présent à la racine du projet `promptachat_fastapi`.

## Lancement de l'application

Pour démarrer le serveur de développement FastAPI avec Uvicorn (avec rechargement automatique) :

```bash
poetry run uvicorn app.main:app --reload
```

Ou si vous n'utilisez pas Poetry mais avez activé un environnement virtuel :

```bash
cd app # Assurez-vous d'être dans le répertoire parent de main.py, ou ajustez le chemin
cd .. # Retour à la racine de promptachat_fastapi
uvicorn app.main:app --reload 
```

L'application sera alors accessible à l'adresse [http://127.0.0.1:8000](http://127.0.0.1:8000).

## Structure du Projet

```
promptachat_fastapi/
├── app/                    # Contient le code source de l'application FastAPI
│   ├── __init__.py
│   ├── main.py             # Point d'entrée principal de l'application FastAPI
│   ├── core/               # Configuration de base, etc.
│   │   ├── __init__.py
│   │   └── config.py       # (Pourrait charger la config depuis config.ini)
│   ├── templates/          # Templates Jinja2
│   │   ├── base.html
│   │   └── index.html
│   └── static/             # Fichiers statiques (CSS, JS, Images)
│       ├── css/
│       │   └── style.css
│       ├── js/             # Fichiers JavaScript personnalisés
│       ├── images/         # Logos et autres images
│       └── vendor/         # Bibliothèques JS/CSS tierces (ex: Toast UI)
│           └── toastui/
├── tests/                  # Tests unitaires et d'intégration
├── .gitignore              # Fichiers et répertoires à ignorer par Git
├── pyproject.toml          # Dépendances du projet (Poetry)
├── README_fastapi.md       # Ce fichier
├── config.ini              # Fichier de configuration (copié de l'original)
├── prompts.json            # Définitions des prompts (copié de l'original)
└── user_auth.db            # Base de données pour l'authentification (si utilisée)
```

## Prochaines étapes (Exemples)

-   Développer les modèles Pydantic pour les données.
-   Créer les routes API (endpoints).
-   Implémenter la logique métier.
-   Développer les templates HTML avec HTMX pour l'interactivité.
-   Mettre en place l'authentification. 