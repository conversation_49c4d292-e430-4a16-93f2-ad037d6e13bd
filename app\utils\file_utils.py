"""
Utilitaires pour la gestion des fichiers.
"""

import io
import PyPDF2
import logging
from typing import List, Any, Optional # Maintenir Any pour le moment, à typer plus précisément avec UploadFile
from fastapi import UploadFile # Importer UploadFile pour le typage

logger = logging.getLogger(__name__)

async def extract_text_from_pdf(pdf_file: UploadFile) -> str:
    """
    Extrait le texte d'un fichier PDF uploadé via FastAPI.
    
    Args:
        pdf_file: Objet UploadFile de FastAPI.
        
    Returns:
        str: Texte extrait du PDF, ou un message d'erreur.
    """
    filename = pdf_file.filename if pdf_file.filename else "fichier_inconnu.pdf"
    try:
        logger.debug(f"Début de l'extraction du texte du PDF: {filename}")
        # Lire le contenu du fichier uploadé en bytes
        pdf_content_bytes = await pdf_file.read()
        
        # Utiliser io.BytesIO pour que PyPDF2 puisse lire les bytes comme un fichier
        pdf_reader = PyPDF2.PdfReader(io.BytesIO(pdf_content_bytes))
        text = ""
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            page_text = page.extract_text()
            if page_text: # S'assurer qu'il y a du texte à ajouter
                text += page_text + "\n\n" # Ajouter un saut de ligne entre les pages
        
        logger.info(f"Texte extrait avec succès du PDF: {filename} ({len(text)} caractères)")
        return text.strip() # Retirer les espaces/sauts de ligne en excès à la fin
    except Exception as e:
        logger.error(f"Erreur lors de l'extraction du texte du PDF {filename}: {e}", exc_info=True)
        # Renvoyer une chaîne d'erreur claire, qui pourrait être affichée à l'utilisateur
        return f"Erreur lors de l'analyse du fichier PDF '{filename}'. Le fichier est peut-être corrompu ou n'est pas un PDF valide. Détail: {str(e)}"

async def get_pdf_content_summary(uploaded_files: List[UploadFile]) -> str:
    """
    Génère un résumé du contenu de tous les fichiers PDF uploadés.
    
    Args:
        uploaded_files: Liste des fichiers UploadFile de FastAPI.
        
    Returns:
        str: Résumé du contenu des PDF au format texte.
    """
    if not uploaded_files:
        return ""
    
    pdf_summary_parts = []
    
    for file in uploaded_files:
        filename = file.filename if file.filename else "fichier_inconnu"
        # Vérifier le type de contenu si possible, ou se fier à l'extension
        if filename.lower().endswith('.pdf') or (file.content_type and file.content_type == "application/pdf"):
            logger.info(f"Traitement du fichier PDF pour résumé: {filename}")
            content_header = f"--- Contenu du fichier: {filename} ---"
            extracted_text = await extract_text_from_pdf(file)
            
            pdf_summary_parts.append(content_header)
            pdf_summary_parts.append(extracted_text)
            pdf_summary_parts.append("--- Fin du fichier ---\n") # Marqueur de fin plus clair
        else:
            logger.warning(f"Fichier ignoré (non PDF): {filename}, type: {file.content_type}")
            
    if not pdf_summary_parts:
        return "Aucun fichier PDF valide n'a été fourni ou traité."
        
    return "\n".join(pdf_summary_parts)

# Exemple d'utilisation (pourrait être dans un endpoint FastAPI plus tard)
# async def process_files_endpoint(files: List[UploadFile] = File(...)):
#     summary = await get_pdf_content_summary(files)
#     # ... faire quelque chose avec le résumé ...
#     return {"summary": summary} 