"""
Routes pour l'authentification (login, logout).
"""
import logging
from typing import Annotated, Dict, Any, Optional

from fastapi import APIRouter, Request, Depends, Form, HTTPException, Response
from fastapi.responses import HTMLResponse, RedirectResponse
from app.main import templates
from starlette.status import HTTP_400_BAD_REQUEST, HTTP_401_UNAUTHORIZED, HTTP_302_FOUND, HTTP_303_SEE_OTHER

from app.auth.ldap_service import LdapService, LdapAuthenticationError, LdapConfigurationError
from app.auth.user_manager import UserManager
from app.auth.security import (
    create_session_cookie, 
    delete_session_cookie, 
    get_current_user, 
    SESSION_COOKIE_NAME,
    get_user_manager,
    create_session,
    delete_session
)
from app.core.config import config_manager
from urllib.parse import urlparse, urlunparse, parse_qs, urlencode

logger = logging.getLogger(__name__)
router = APIRouter()

_ldap_service_instance: Optional[LdapService] = None

def get_ldap_service() -> Optional[LdapService]:
    global _ldap_service_instance
    if not config_manager.is_ldap_enabled():
        if _ldap_service_instance is not None:
            logger.info("LDAP désactivé, suppression de l'instance LdapService existante.")
            _ldap_service_instance = None
        return None
        
    if _ldap_service_instance is None:
        try:
            _ldap_service_instance = LdapService()
            logger.info("Instance LdapService créée.")
        except LdapConfigurationError as e:
            logger.error(f"Erreur de configuration LDAP lors de l'initialisation pour les routes: {e}")
            _ldap_service_instance = None
    return _ldap_service_instance

@router.get("/login", response_class=HTMLResponse, name="login_page")
async def login_page_get(
    request: Request, 
    error_message: Optional[str] = None, 
    success_message: Optional[str] = None,
    return_to: Optional[str] = None
):
    if request.cookies.get(SESSION_COOKIE_NAME):
        try:
            user_manager_instance = get_user_manager()
            await get_current_user(request=request, user_manager=user_manager_instance)
            if return_to:
                parsed_url = urlparse(return_to)
                query_params = parse_qs(parsed_url.query)
                if 'session_id' in query_params: del query_params['session_id']
                cleaned_return_to = urlunparse(parsed_url._replace(query=urlencode(query_params, doseq=True)))
                logger.info(f"Déjà connecté. Redirection vers return_to nettoyé: {cleaned_return_to}")
                return RedirectResponse(url=cleaned_return_to, status_code=HTTP_302_FOUND)
            logger.info(f"Déjà connecté. Redirection vers la racine.")
            return RedirectResponse(url=request.app.url_path_for("root"), status_code=HTTP_302_FOUND)
        except HTTPException:
            pass 

    login_disabled_message = None
    if not config_manager.is_ldap_enabled():
        login_disabled_message = "L'authentification LDAP est actuellement désactivée. Vous pouvez vous connecter avec n'importe quelles informations pour accéder en tant que développeur."

    return templates.TemplateResponse("auth/login.html", {
        "request": request,
        "error_message": error_message,
        "success_message": success_message,
        "ldap_enabled": config_manager.is_ldap_enabled(),
        "login_disabled_message": login_disabled_message,
        "return_to": return_to
    })

@router.post("/login", response_class=HTMLResponse, name="login_submit")
async def login_submit_post(
    request: Request, 
    response: Response, 
    username: str = Form(...),
    password: str = Form(...),
    return_to: Optional[str] = Form(None),
    user_manager: UserManager = Depends(get_user_manager)
):
    error_message_login_page: Optional[str] = None

    if not config_manager.is_ldap_enabled():
        logger.info(f"LDAP est désactivé. Tentative de connexion avec username: {username}")
        is_app_authorized, user_app_details = user_manager.check_authorization(username)
        
        if is_app_authorized:
            logger.info(f"Utilisateur '{username}' autorisé et connecté en mode développeur.")
            session_id = create_session(user_app_details)
            
            final_redirect_url = "/" 
            if return_to:
                parsed_url = urlparse(return_to)
                query_params = parse_qs(parsed_url.query)
                if 'session_id' in query_params: del query_params['session_id']
                query_params['session_id'] = [session_id]
                final_redirect_url = urlunparse(parsed_url._replace(query=urlencode(query_params, doseq=True)))
                logger.info(f"Redirection après login (LDAP off) vers (return_to nettoyé + session_id): {final_redirect_url}")
            else:
                final_redirect_url = f"/?session_id={session_id}"
                logger.info(f"Redirection après login (LDAP off) vers (par défaut): {final_redirect_url}")
            
            # Créer la réponse de redirection puis définir le cookie
            redirect_response = RedirectResponse(url=final_redirect_url, status_code=HTTP_302_FOUND)
            create_session_cookie(redirect_response, session_id)
            return redirect_response
        else:
            error_message_login_page = f"Erreur critique: impossible d'autoriser l'utilisateur '{username}' en mode développeur."
            status_code_on_error = HTTP_400_BAD_REQUEST
    else:
        ldap_service = get_ldap_service()
        if not ldap_service:
            error_message_login_page = "Service LDAP non initialisé."
            status_code_on_error = HTTP_500_INTERNAL_SERVER_ERROR
        else:
            try:
                is_ldap_auth_success, user_ldap_info = ldap_service.authenticate(username, password)
                if is_ldap_auth_success and user_ldap_info:
                    uid = user_ldap_info.get("uid")
                    if not uid:
                        error_message_login_page = "Erreur interne LDAP (UID manquant)."
                        status_code_on_error = HTTP_500_INTERNAL_SERVER_ERROR
                    else:
                        is_app_authorized, user_app_details = user_manager.check_authorization(uid)
                        if is_app_authorized:
                            logger.info(f"Utilisateur '{uid}' authentifié et autorisé (LDAP).")
                            session_id = create_session(user_app_details)
                            
                            final_redirect_url = "/"
                            if return_to:
                                parsed_url = urlparse(return_to)
                                query_params = parse_qs(parsed_url.query)
                                if 'session_id' in query_params: del query_params['session_id']
                                query_params['session_id'] = [session_id]
                                final_redirect_url = urlunparse(parsed_url._replace(query=urlencode(query_params, doseq=True)))
                                logger.info(f"Redirection après login (LDAP on) vers (return_to nettoyé + session_id): {final_redirect_url}")
                            else:
                                final_redirect_url = f"/?session_id={session_id}"
                                logger.info(f"Redirection après login (LDAP on) vers (par défaut): {final_redirect_url}")
                            
                            if "HX-Request" in request.headers:
                                response.headers["HX-Redirect"] = final_redirect_url
                                create_session_cookie(response, session_id)
                                return Response(content="", status_code=200) 
                            
                            # Créer la réponse de redirection puis définir le cookie
                            redirect_response = RedirectResponse(url=final_redirect_url, status_code=HTTP_302_FOUND)
                            create_session_cookie(redirect_response, session_id)
                            return redirect_response
                        else:
                            contact_email = config_manager.get_access_contact_email()
                            error_message_login_page = f"Accès refusé. Compte '{uid}' non autorisé. Contact: {contact_email}"
                            status_code_on_error = HTTP_403_FORBIDDEN
                else:
                    error_message_login_page = "Nom d'utilisateur ou mot de passe invalide."
                    status_code_on_error = HTTP_401_UNAUTHORIZED
            except LdapAuthenticationError as e:
                error_message_login_page = str(e)
                status_code_on_error = HTTP_401_UNAUTHORIZED
            except LdapConfigurationError as e:
                error_message_login_page = "Erreur de configuration LDAP. Contactez l'administrateur."
                logger.error(f"Erreur config LDAP auth '{username}': {e}", exc_info=True)
                status_code_on_error = HTTP_500_INTERNAL_SERVER_ERROR
            except Exception as e:
                logger.error(f"Erreur inattendue login '{username}': {e}", exc_info=True)
                error_message_login_page = "Une erreur inattendue est survenue."
                status_code_on_error = HTTP_500_INTERNAL_SERVER_ERROR

    if error_message_login_page:
        logger.warning(f"Échec du login pour '{username}': {error_message_login_page}")
        if "HX-Request" in request.headers:
            return templates.TemplateResponse("auth/login.html", {
                "request": request, "error_message": error_message_login_page, "ldap_enabled": config_manager.is_ldap_enabled(), "username_submitted": username, "return_to": return_to
            }, status_code=status_code_on_error) 
        return templates.TemplateResponse("auth/login.html", {
            "request": request, "error_message": error_message_login_page, "ldap_enabled": config_manager.is_ldap_enabled(), "username_submitted": username, "return_to": return_to
        }, status_code=status_code_on_error)
    
    return RedirectResponse(url=request.app.url_path_for("login_page"), status_code=HTTP_302_FOUND)

@router.post("/logout", name="logout")
async def logout_post(request: Request, response: Response, current_user: Optional[Dict[str, Any]] = Depends(get_current_user)):
    uid = "unknown_user"
    if current_user:
        uid = current_user.get("uid", "unknown_user_no_uid_key")
    
    session_id = request.cookies.get(SESSION_COOKIE_NAME) 
    if not session_id and hasattr(request.state, 'session_id'):
        session_id = request.state.session_id
    
    if session_id:
        delete_session(session_id)
    
    delete_session_cookie(response)
    logger.info(f"Utilisateur '{uid}' déconnecté.")
    
    success_message = "Déconnexion réussie"
    redirect_url = request.app.url_path_for("login_page") + f"?success_message={success_message}"
    
    if "HX-Request" in request.headers:
        response.headers["HX-Redirect"] = redirect_url
        return Response(content="", status_code=200)
    return RedirectResponse(url=redirect_url, status_code=HTTP_302_FOUND)

@router.get("/check-auth", name="check_auth_status")
async def check_auth_status_get(current_user: Optional[Dict[str, Any]] = Depends(get_current_user)):
    return {"authenticated": True, "user": current_user}

# On pourrait aussi ajouter une route GET /me pour récupérer les infos de l'utilisateur connecté
# @router.get("/users/me", name="read_users_me")
# async def read_users_me(current_user: Dict[str, Any] = Depends(get_current_user)):
#     return current_user 