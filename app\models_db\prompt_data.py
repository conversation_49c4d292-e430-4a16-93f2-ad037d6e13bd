"""
Modèle de données pour la gestion des prompts système.
"""

import json
import logging
import shutil
from typing import Dict, List, Any, Optional
from pathlib import Path

# Importer le chemin racine du projet défini dans la config
from app.core.config import PROJECT_ROOT_DIR

logger = logging.getLogger(__name__)

# Définir le chemin complet vers prompts.json
PROMPTS_JSON_PATH = PROJECT_ROOT_DIR / "prompts.json"

class PromptData:
    """
    Classe pour gérer les données des prompts système à partir du fichier prompts.json.
    """

    def __init__(self, file_path: Path = PROMPTS_JSON_PATH):
        """
        Initialise la classe PromptData.

        Args:
            file_path (Path): Chemin vers le fichier JSON contenant les prompts système.
        """
        self.file_path = file_path
        if file_path != PROMPTS_JSON_PATH:
            logger.warning(
                f"PromptData est typiquement initialisé avec {PROMPTS_JSON_PATH}, "
                f"mais {file_path} a été fourni. Utilisation de {file_path}."
            )
        self.prompts_data: Optional[Dict[str, List[Dict[str, Any]]]] = None
        self.load_prompts()

    def load_prompts(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Charge les prompts système depuis le fichier JSON.

        Returns:
            dict: Dictionnaire contenant les prompts par catégorie.
                  Retourne une structure vide en cas d'erreur.
        """
        try:
            with self.file_path.open("r", encoding="utf-8") as f:
                self.prompts_data = json.load(f)
            logger.info(f"Prompts système chargés depuis {self.file_path}")

            if not isinstance(self.prompts_data, dict):
                logger.error(f"Le contenu de {self.file_path} n'est pas un dictionnaire JSON valide. Initialisation vide.")
                self.prompts_data = {"internal": [], "external": []}
                # Ne pas sauvegarder automatiquement un fichier potentiellement corrompu sans action explicite
                return self.prompts_data.copy() # Retourner une copie

            required_categories = ["internal", "external"]
            data_updated_or_malformed = False

            for category in required_categories:
                if category not in self.prompts_data or not isinstance(self.prompts_data.get(category), list):
                    logger.warning(f"Catégorie '{category}' manquante ou invalide dans {self.file_path}. Initialisation avec une liste vide pour cette catégorie.")
                    self.prompts_data[category] = []
                    data_updated_or_malformed = True

            for category_name, category_list in self.prompts_data.items():
                if not isinstance(category_list, list):
                    logger.warning(f"Contenu invalide pour la catégorie '{category_name}' (devrait être une liste). Remplacement par une liste vide.")
                    self.prompts_data[category_name] = []
                    data_updated_or_malformed = True
                    continue
                
                valid_prompts_in_category = []
                for prompt in category_list:
                    if isinstance(prompt, dict) and "id" in prompt and "title" in prompt and "content" in prompt:
                        prompt.setdefault("variables", [])
                        prompt.setdefault("accepts_files", False)
                        prompt.setdefault("needs_cockpit", False)
                        prompt.setdefault("welcome_page_html", "")
                        valid_prompts_in_category.append(prompt)
                    else:
                        logger.warning(f"Prompt invalide ou manquant de champs essentiels (id, title, content) dans la catégorie '{category_name}' et ignoré: {str(prompt)[:100]}...")
                        data_updated_or_malformed = True
                self.prompts_data[category_name] = valid_prompts_in_category
            
            # if data_updated_or_malformed:
            #     logger.info(f"La structure de {self.file_path} a été vérifiée/corrigée.")
                # Envisager de sauvegarder ici seulement si une action de l'utilisateur le justifie
                # ou si le fichier était inexistant et a été créé avec une structure par défaut.

            return self.prompts_data.copy() # Retourner une copie

        except FileNotFoundError:
            logger.error(f"Fichier de prompts système '{self.file_path}' introuvable. Initialisation avec une structure vide.")
            self.prompts_data = {"internal": [], "external": []}
            # Tentative de création d'un fichier prompts.json vide et structuré si non trouvé
            # self.save_prompts() # Attention: crée le fichier s'il n'existe pas
            return self.prompts_data.copy()
        except json.JSONDecodeError as e:
            logger.error(f"Erreur de décodage JSON du fichier de prompts système '{self.file_path}': {e}")
            self.prompts_data = {"internal": [], "external": []}
            return self.prompts_data.copy()
        except Exception as e:
            logger.error(f"Erreur inattendue lors du chargement de {self.file_path}: {e}", exc_info=True)
            self.prompts_data = {"internal": [], "external": []}
            return self.prompts_data.copy()

    def get_prompt(self, prompt_id: str) -> Optional[Dict[str, Any]]:
        if not self.prompts_data:
            self.load_prompts()
        if not self.prompts_data: return None

        for category_list in self.prompts_data.values():
            if isinstance(category_list, list):
                for prompt in category_list:
                    if isinstance(prompt, dict) and prompt.get("id") == prompt_id:
                        return prompt.copy() # Retourner une copie
        logger.debug(f"Prompt système avec ID '{prompt_id}' non trouvé.")
        return None

    def get_prompts_by_category(self, category: str) -> List[Dict[str, Any]]:
        if not self.prompts_data:
            self.load_prompts()
        if not self.prompts_data: return []

        logger.info(f"Récupération des prompts système pour la catégorie: {category}")
        logger.info(f"Catégories disponibles: {list(self.prompts_data.keys())}")
        
        category_prompts = self.prompts_data.get(category, [])
        if isinstance(category_prompts, list):
            result = [p.copy() for p in category_prompts if isinstance(p, dict)]
            logger.info(f"Nombre de prompts système trouvés pour la catégorie '{category}': {len(result)}")
            return result
        
        logger.warning(f"La catégorie '{category}' n'est pas une liste valide dans prompts.json")
        return []

    def get_categories(self) -> List[str]:
        if not self.prompts_data:
            self.load_prompts()
        if not self.prompts_data: return []
        return [cat for cat in ["internal", "external"] if cat in self.prompts_data and isinstance(self.prompts_data.get(cat), list)]

    def find_category_for_prompt(self, prompt_id: str) -> Optional[str]:
        if not self.prompts_data:
            self.load_prompts()
        if not self.prompts_data: return None

        for category_name, prompts_list in self.prompts_data.items():
            if isinstance(prompts_list, list):
                if any(isinstance(p, dict) and p.get("id") == prompt_id for p in prompts_list):
                    return category_name
        return None

    def add_prompt(self, category: str, prompt_data: Dict[str, Any]) -> bool:
        logger.warning(f"Ajout programmatique d'un prompt système '{prompt_data.get('id')}'. Action réservée aux admins.")
        if not self.prompts_data: self.load_prompts()
        if not self.prompts_data or category not in self.prompts_data or not isinstance(self.prompts_data.get(category), list):
            logger.error(f"Catégorie système '{category}' non trouvée ou invalide pour ajout.")
            return False

        prompt_id = prompt_data.get("id")
        if not prompt_id or not prompt_data.get("title") or not prompt_data.get("content"):
            logger.error("Le prompt système doit avoir au moins un ID, un titre et un contenu.")
            return False
        if self.get_prompt(prompt_id):
            logger.error(f"Un prompt système avec l'ID '{prompt_id}' existe déjà.")
            return False
        
        # Assurer la présence des champs par défaut
        prompt_data.setdefault("variables", [])
        prompt_data.setdefault("accepts_files", False)
        prompt_data.setdefault("needs_cockpit", False)
        prompt_data.setdefault("welcome_page_html", "")

        self.prompts_data[category].append(prompt_data)
        return self.save_prompts()

    def update_prompt(self, prompt_id: str, updated_data: Dict[str, Any]) -> bool:
        logger.warning(f"Mise à jour du prompt système '{prompt_id}'. Action réservée aux admins.")
        if not self.prompts_data: self.load_prompts()
        if not self.prompts_data: return False

        category = self.find_category_for_prompt(prompt_id)
        if not category or not isinstance(self.prompts_data.get(category), list):
            logger.error(f"Prompt système ID '{prompt_id}' non trouvé ou catégorie invalide pour mise à jour.")
            return False

        found_and_updated = False
        for i, prompt in enumerate(self.prompts_data[category]):
            if isinstance(prompt, dict) and prompt.get("id") == prompt_id:
                # S'assurer que l'ID n'est pas modifié par updated_data
                original_id = prompt.get("id")
                prompt.update(updated_data) # Met à jour le dictionnaire existant
                prompt["id"] = original_id # Rétablir l'ID original au cas où
                
                # Assurer la présence des champs par défaut si absents après mise à jour
                prompt.setdefault("variables", [])
                prompt.setdefault("accepts_files", False)
                prompt.setdefault("needs_cockpit", False)
                prompt.setdefault("welcome_page_html", "")
                found_and_updated = True
                break
        
        if found_and_updated:
            return self.save_prompts()
        else:
            logger.error(f"Prompt système ID '{prompt_id}' non trouvé dans la catégorie '{category}' pour mise à jour interne.")
            return False

    def delete_prompt(self, prompt_id: str) -> bool:
        logger.error(f"Tentative de suppression du prompt système '{prompt_id}'. Action interdite pour PromptData.")
        return False # Les prompts système ne sont pas supprimables par cette interface

    def save_prompts(self) -> bool:
        if self.prompts_data is None: # Vérifier si prompts_data est None
            logger.error("Tentative de sauvegarde de prompts système sans données (None). Annulation.")
            return False

        backup_path = self.file_path.with_suffix(self.file_path.suffix + ".bak")
        try:
            if self.file_path.exists():
                try:
                    shutil.copy2(str(self.file_path), str(backup_path))
                    logger.info(f"Backup de {self.file_path} créé : {backup_path}")
                except Exception as backup_err:
                    logger.error(f"Erreur lors de la création du backup pour {self.file_path}: {backup_err}")
            
            # S'assurer que le répertoire parent existe
            self.file_path.parent.mkdir(parents=True, exist_ok=True)

            with self.file_path.open("w", encoding="utf-8") as f:
                json.dump(self.prompts_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Prompts système sauvegardés dans {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Erreur majeure lors de la sauvegarde des prompts système dans {self.file_path}: {e}", exc_info=True)
            return False

# Instance unique pour un accès facile (optionnel, dépend de l'architecture)
# prompt_data_manager = PromptData() 