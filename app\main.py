from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from pathlib import Path
from fastapi import Depends
from fastapi.responses import RedirectResponse, JSONResponse, PlainTextResponse
from datetime import datetime

# Import des modules de configuration et de logging
from app.core.config import config_manager
from app.utils.logging_utils import setup_logging
import logging # Importer le module logging pour obtenir un logger

# Configuration du logging dès le début
# Les paramètres sont lus depuis config_manager
setup_logging(
    log_level_str=config_manager.get_default_log_level(),
    log_file_path_str=config_manager.get_log_file()
)

# Obtenir un logger pour ce module (main.py)
logger = logging.getLogger(__name__)

# Définir le chemin de base du projet pour faciliter l'accès aux fichiers
BASE_DIR = Path(__file__).resolve().parent.parent
APP_DIR = Path(__file__).resolve().parent # C'est promptachat_fastapi/app/

# Configurer Jinja2Templates AVANT d'importer les routeurs qui pourraient l'utiliser
templates = Jinja2Templates(directory=str(APP_DIR / "templates"))
# Ajouter config_manager aux globales de Jinja2 pour y accéder dans les templates
templates.env.globals['config'] = config_manager

app = FastAPI()

# Import du routeur d'authentification
from app.apis import routes_auth
# Import du nouveau routeur pour les prompts
from app.apis import routes_prompts
# Import pour la dépendance et la redirection
from app.auth.security import get_current_user, SESSION_COOKIE_NAME, get_user_manager

# --- AJOUT : Événement de démarrage pour logger que l'application a démarré ---
@app.on_event("startup")
async def startup_event():
    logger.info("L'application FastAPI démarre...")
    logger.info(f"Nom de l'application (depuis config): {config_manager.get_app_name()}")
    logger.info(f"LDAP activé (depuis config): {config_manager.is_ldap_enabled()}")
    # Vous pouvez ajouter d'autres logs de configuration importants ici
# --- FIN AJOUT ---

# Monter le répertoire static
app.mount("/static", StaticFiles(directory=str(APP_DIR / "static")), name="static")

# Monter le répertoire screenshots s'il existe, sinon créer un gestionnaire simple
screenshots_dir = APP_DIR / "screenshots"
if screenshots_dir.exists():
    app.mount("/screenshots", StaticFiles(directory=str(screenshots_dir)), name="screenshots")
else:
    @app.get("/screenshots/{filename}")
    async def get_screenshot(filename: str):
        """Gestionnaire pour les screenshots manquants"""
        return JSONResponse({"error": "Screenshot not found"}, status_code=404)

# Inclure le routeur d'authentification
app.include_router(routes_auth.router, prefix="/auth", tags=["Authentication"])
# Inclure le routeur des prompts
app.include_router(routes_prompts.router, prefix="/prompts", tags=["Prompts"])

@app.get("/home", name="home")
async def home_page(request: Request):
    """Page d'accueil publique qui vérifie la présence d'un cookie de session"""
    session_id = request.cookies.get(SESSION_COOKIE_NAME)
    if session_id:
        try:
            user_manager = get_user_manager()
            user = await get_current_user(request=request, user_manager=user_manager)
            return templates.TemplateResponse(
                "index.html", 
                {"request": request, "user": user}
            )
        except HTTPException:
            # Si le cookie existe mais n'est pas valide, rediriger vers login
            return RedirectResponse(url=app.url_path_for("login_page"), status_code=302)
    else:
        # Si pas de cookie, rediriger vers login
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=302)

# Middleware pour la protection des routes
@app.middleware("http")
async def auth_middleware(request: Request, call_next):
    # # Si LDAP est désactivé, ne pas appliquer le middleware d'authentification
    # if not config_manager.is_ldap_enabled():
    #     logger.debug("LDAP désactivé, middleware d'authentification contourné.")
    #     request.state.user = None 
    #     response = await call_next(request)
    #     return response

        # Logique originale du middleware
    session_id_cookie = request.cookies.get(SESSION_COOKIE_NAME)
    
    public_paths = [
        app.url_path_for("login_page"), 
        app.url_path_for("login_submit"),
        app.url_path_for("home"),
        # Nous ne mettons pas "root" ici car il nécessite une authentification
    ]
    
    # Chemins à exclure complètement du middleware (health checks, monitoring, etc.)
    excluded_paths = [
        "/health",
        "/latest_answer",
        "/favicon.ico",
    ]
    
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"

    # Exclure les fichiers statiques
    if request.url.path.startswith("/static/"):
        response = await call_next(request)
        return response

    # Exclure les screenshots et autres fichiers
    if request.url.path.startswith("/screenshots/"):
        response = await call_next(request)
        return response

    # Exclure les routes d'authentification
    if request.url.path.startswith(app.url_path_for("login_page").rsplit("/",1)[0]): # Couvre /auth/
        response = await call_next(request)
        return response
    
    # Exclure les chemins de monitoring/health checks
    if request.url.path in excluded_paths:
        response = await call_next(request)
        return response

    if request.url.path not in public_paths:
        session_id_url_or_form = request.query_params.get("session_id")

        if request.method == "POST" and not session_id_url_or_form:
            try:
                form_data = await request.form()
                session_id_from_form = form_data.get("session_id")
                if session_id_from_form:
                    logger.debug(f"Middleware: Session ID formulaire POST: {session_id_from_form}")
                    session_id_url_or_form = session_id_from_form
            except Exception as e:
                logger.debug(f"Middleware: Erreur lecture formulaire POST: {e}")

        # Import de la fonction de validation au début
        from app.auth.security import get_session as validate_session
        
        valid_session_found = False
        valid_session_id = None
        
        # Prioriser le cookie sur l'URL/form pour la sécurité
        if session_id_cookie:
            user = validate_session(session_id_cookie)
            if user:
                request.state.session_id = session_id_cookie
                valid_session_found = True
                valid_session_id = session_id_cookie
            # else: session invalide mais pas de log pour éviter le spam

        # Si pas de cookie valide, essayer URL/form
        if not valid_session_found and session_id_url_or_form:
            user = validate_session(session_id_url_or_form)
            if user:
                logger.info(f"Middleware: Session URL validée pour {user.get('uid')}")
                request.state.session_id = session_id_url_or_form
                valid_session_found = True
                valid_session_id = session_id_url_or_form
            # else: session URL invalide mais pas de log pour éviter le spam
        
        if valid_session_found:
            response = await call_next(request)
            
            # Si la session a été validée via URL/form mais qu'il n'y a pas de cookie,
            # définir le cookie pour les requêtes futures
            if valid_session_id and not session_id_cookie and valid_session_id == session_id_url_or_form:
                from app.auth.security import create_session_cookie
                create_session_cookie(response, valid_session_id)
                # Logger uniquement quand on définit un nouveau cookie
                logger.info(f"Middleware: Cookie de session défini pour l'utilisateur")
            
            return response

        # Si aucune session valide - ne pas logger pour éviter le spam
        if is_ajax:
            return JSONResponse(
                content={"error": "Session invalide ou expirée"},
                status_code=401,
                headers={
                    "X-Login-Required": "true",
                    "X-Login-URL": app.url_path_for("login_page"),
                    "Access-Control-Expose-Headers": "X-Login-Required, X-Login-URL"
                }
            )
        else:
            redirect_response = RedirectResponse(url=app.url_path_for("login_page"), status_code=302)
            if session_id_cookie: # S'il y avait un cookie (forcément invalide à ce stade)
                 # Utiliser la fonction de suppression de cookie existante
                 from app.auth.security import delete_session_cookie
                 delete_session_cookie(redirect_response)
            return redirect_response
            
    response = await call_next(request)
    return response

# Routes de monitoring publiques (sans authentification)
@app.get("/health")
async def health_check():
    """Endpoint de health check"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.get("/latest_answer")
async def latest_answer():
    """Endpoint pour récupérer la dernière réponse (monitoring)"""
    return {"status": "no_data", "timestamp": datetime.now().isoformat()}

# Route simple pour tester HTMX
@app.get("/test-htmx", name="test_htmx")
async def test_htmx(request: Request, user_manager=Depends(get_user_manager)):
    """Route simple pour tester HTMX"""
    try:
        user = await get_current_user(request=request, user_manager=user_manager)
        current_time = datetime.now().strftime("%H:%M:%S")
        return PlainTextResponse(
            f"✅ HTMX fonctionne correctement ! Heure: {current_time} - Utilisateur: {user['uid']}"
        )
    except Exception as e:
        logger.error(f"Erreur dans test-htmx: {e}")
        return PlainTextResponse(f"❌ Erreur HTMX: {str(e)}", status_code=500)

@app.get("/", name="root") # Ajout du nom pour url_for
async def read_root(request: Request):
    """Page racine qui vérifie si l'utilisateur est connecté"""
    session_id = request.query_params.get("session_id")
    
    if session_id:
        # Si on a un session_id dans l'URL, vérifier s'il est valide
        from app.auth.security import get_session
        user = get_session(session_id)
        
        if user:
            # Ajouter le session_id au contexte pour qu'il soit disponible dans les templates
            return templates.TemplateResponse(
                "index.html", 
                {"request": request, "user": user, "session_id": session_id}
            )
    
    # Si pas de session_id dans l'URL ou session invalide, vérifier les cookies
    session_id = request.cookies.get(SESSION_COOKIE_NAME)
    
    if session_id:
        try:
            user_manager = get_user_manager()
            user = await get_current_user(request=request, user_manager=user_manager)
            return templates.TemplateResponse(
                "index.html", 
                {"request": request, "user": user}
            )
        except HTTPException as e:
            logger.warning(f"Authentification échouée pour la page racine: {e}")
            # Rediriger vers la page de connexion
            return RedirectResponse(url=app.url_path_for("login_page"), status_code=302)
    else:
        # Pas de cookie, rediriger vers login
        return RedirectResponse(url=app.url_path_for("login_page"), status_code=302) 