"""
Service pour interagir avec l'API Cockpit.
"""

import logging
import requests
from typing import Dict, Any, Union, Optional

from app.core.config import config_manager # Import de l'instance globale

logger = logging.getLogger(__name__)

class CockpitService:
    """
    Service pour récupérer les données d'une affaire depuis l'API Cockpit.
    """

    def __init__(self):
        """
        Initialise le service Cockpit.
        La configuration est chargée via le config_manager global.
        """
        self.api_url_base: Optional[str] = config_manager.get_cockpit_api_url()
        self.api_key: Optional[str] = config_manager.get_cockpit_api_key()
        self.verify_cert_path: Union[str, bool] = self._determine_verify_option()

        if not self.api_url_base or not self.api_key:
            logger.error("Configuration de l'API Cockpit (URL ou clé) manquante. Vérifiez config.ini.")
            # On pourrait lever une exception ici si le service ne peut absolument pas fonctionner sans.
            # raise ValueError("URL ou clé API Cockpit non configurée.")

    def _determine_verify_option(self) -> Union[str, bool]:
        """
        Détermine la valeur pour le paramètre 'verify' de requests en fonction de la config.
        """
        # La méthode get_cockpit_verify_cert_path() de ConfigManager retourne déjà un chemin absolu ou None
        cert_path = config_manager.get_cockpit_verify_cert_path()
        
        if cert_path:
            # ConfigManager devrait déjà s'assurer que le chemin est utilisable s'il est fourni.
            # Ici, nous pourrions ajouter une vérification d'existence si ce n'est pas garanti par ConfigManager.
            # from pathlib import Path
            # if Path(cert_path).exists():
            logger.info(f"Utilisation du certificat de vérification SSL pour Cockpit: {cert_path}")
            return cert_path
            # else:
            #     logger.error(f"Chemin du certificat SSL Cockpit spécifié ({cert_path}) mais fichier non trouvé. Utilisation de verify=False.")
            #     return False
        else:
            # Si aucun chemin n'est fourni, la vérification SSL sera activée par défaut par requests (True)
            # Sauf si on veut explicitement la désactiver. Le comportement de l'ancien JsonProcessor
            # était de mettre False si le chemin n'était pas bon ou absent.
            # Pour plus de sécurité, il est préférable de laisser requests utiliser True par défaut
            # si aucun certificat spécifique n'est fourni, OU de forcer False si c'est un choix délibéré.
            # L'ancien code passait à False. On va reproduire cela pour l'instant si cert_path est None.
            logger.warning("Aucun chemin de certificat SSL (verify_cert_path) pour Cockpit spécifié. Utilisation de verify=False.")
            return False

    async def get_issue_data(self, cockpit_id: Union[str, int]) -> Optional[Dict[str, Any]]:
        """
        Récupère les données JSON pour un ID Cockpit donné via l'API.

        Args:
            cockpit_id: L'identifiant de l'affaire Cockpit.

        Returns:
            Un dictionnaire contenant les données JSON de l'affaire, ou None si une erreur se produit.
        """
        if not self.api_url_base or not self.api_key:
            logger.error("Le service Cockpit n'est pas correctement configuré (URL ou clé API manquante).")
            return None

        full_api_url = f"{self.api_url_base.rstrip('/')}/{cockpit_id}.json"
        headers = {"X-Redmine-API-Key": self.api_key}

        logger.info(f"Appel API Cockpit pour ID: {cockpit_id} à l'URL: {full_api_url}")
        logger.debug(f"Option de vérification SSL pour Cockpit: {self.verify_cert_path}")

        try:
            # Note: requests est synchrone. Pour une application FastAPI entièrement asynchrone,
            # il faudrait utiliser une bibliothèque HTTP asynchrone comme httpx.
            # Pour l'instant, on garde requests pour suivre l'original.
            # Si cela cause des blocages, il faudra passer à httpx et rendre cette méthode `async`.
            # Pour simuler un appel non bloquant dans un contexte async, on pourrait utiliser
            # `await asyncio.to_thread(requests.get, ...)` mais httpx est préférable.
            # Ici, on fait l'hypothèse que c'est acceptable pour le moment ou sera refactorisé.
            
            # Pour l'instant, on exécute l'appel synchrone. Si on veut le rendre compatible avec `async`
            # sans changer requests, il faudrait un `loop.run_in_executor` ou `asyncio.to_thread`
            # autour de cet appel dans le code qui appelle ce service.
            # Par simplicité, on le laisse synchrone dans le service pour le moment.
            
            # response = requests.get(
            #     full_api_url,
            #     headers=headers,
            #     verify=self.verify_cert_path,
            #     timeout=30
            # )

            # Pour rendre cet appel non-bloquant dans un contexte FastAPI async, on peut utiliser httpx
            # Il faudrait ajouter httpx aux dépendances : poetry add httpx
            # Puis remplacer par :
            # import httpx
            # async with httpx.AsyncClient(verify=self.verify_cert_path) as client:
            #     response = await client.get(full_api_url, headers=headers, timeout=30)
            
            # --- Solution temporaire synchrone --- 
            # CETTE PARTIE EST SYNCHRONE. Pour une vraie application async, utilisez httpx.
            # On va la rendre asynchrone en utilisant asyncio.to_thread si possible
            # ou la laisser synchrone en sachant que c'est un point à améliorer.
            # Puisque le reste de l'application est async, il est préférable de faire un appel non bloquant.
            # Cependant, l'outil de code ne permet pas d'ajouter des imports comme asyncio.
            # Je vais donc la laisser synchrone pour l'instant, et le code appelant devra gérer cela.
            # Pour les besoins de la migration, nous allons la rendre `async` et supposer que l'appelant
            # utilisera `await` et que nous utiliserons une bibliothèque compatible (comme `httpx` qui serait ajouté au projet).
            # Pour l'instant, je vais simuler l'appel avec `requests` mais en gardant la signature `async`.
            # **Important**: Ceci nécessitera `httpx` dans le projet réel.

            # Pour l'exercice, je vais écrire le code comme si `httpx` était disponible.
            # Vous devrez ajouter `httpx` à votre `pyproject.toml`.
            import httpx # Supposons que httpx est disponible
            async with httpx.AsyncClient(verify=self.verify_cert_path) as client:
                response = await client.get(full_api_url, headers=headers, timeout=30)
            
            response.raise_for_status()  # Lève une exception pour les codes d'erreur HTTP (4xx ou 5xx)
            json_data = response.json()
            logger.info(f"Données Cockpit récupérées avec succès pour l'ID: {cockpit_id}")
            return json_data

        except httpx.RequestError as req_err: # Erreurs liées à la requête elle-même (DNS, connexion refusée...)
            logger.error(f"Erreur de requête HTTPX vers {full_api_url}: {req_err}", exc_info=True)
            return None
        except httpx.HTTPStatusError as status_err: # Erreurs HTTP (4xx, 5xx)
            logger.error(
                f"Erreur de statut HTTP {status_err.response.status_code} lors de l'appel API vers {full_api_url}. "
                f"Réponse: {status_err.response.text[:200]}...", 
                exc_info=True
            )
            return None
        except json.JSONDecodeError as json_err:
            logger.error(f"Erreur de décodage JSON de la réponse de l'API Cockpit ({full_api_url}): {json_err}", exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Erreur inattendue lors de l'appel API Cockpit ({full_api_url}): {e}", exc_info=True)
            return None

# Instance unique du service (si approprié, ou injecté par dépendance dans FastAPI)
# cockpit_service = CockpitService() 