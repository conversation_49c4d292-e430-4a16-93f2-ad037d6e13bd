"""
Service pour la gestion et la récupération des prompts.
"""
import logging
from typing import List, Dict, Any, Optional

from app.models_db.prompt_data import PromptData
from app.models_db.user_prompt_data import UserPromptData
from app.utils.cockpit_json_helper import CockpitJsonHelper # Pour les labels des variables cockpit
# Assurez-vous que PROJECT_ROOT_DIR est accessible si UserPromptData en a besoin directement
# ou que UserPromptData est initialisé avec le user_uid pour charger les bons prompts.

logger = logging.getLogger(__name__)

# Mapping des slugs de catégorie d'URL vers les noms de catégorie JSON (clés dans prompts.json)
CATEGORY_SLUG_TO_JSON_KEY = {
    "internal": "internal", # Slug "internal" correspond à la clé "internal" dans le JSON
    "external": "external", # Slug "external" correspond à la clé "external" dans le JSON
}
# Noms affichables pour les catégories (utilisés dans les titres, etc.)
CATEGORY_SLUG_TO_DISPLAY_NAME = {
    "internal": "Données Internes",
    "external": "Données Externes",
}

class PromptService:
    """
    Service pour récupérer et préparer les listes et détails des prompts.
    """

    def __init__(self):
        """
        Initialise le service.
        Pour l'instant, PromptData est instancié à chaque appel ou pourrait être
        passé en dépendance si sa création est coûteuse et qu'il peut être partagé.
        UserPromptData est spécifique à l'utilisateur, donc doit être initialisé avec user_uid.
        """
        self.prompt_data_manager = PromptData() # Instance pour les prompts système
        self.user_prompt_data_manager = UserPromptData() # Instance pour les prompts utilisateur
        self.cockpit_helper = CockpitJsonHelper() # Pour obtenir les labels des champs cockpit

    def get_prompts_for_category(self, category_slug: str, user_uid: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Récupère et fusionne les prompts système et utilisateur pour une catégorie donnée.
        Si user_uid est fourni, inclut les prompts de cet utilisateur.
        """
        json_category_key = CATEGORY_SLUG_TO_JSON_KEY.get(category_slug)
        category_display_name = CATEGORY_SLUG_TO_DISPLAY_NAME.get(category_slug, category_slug.capitalize())

        if not json_category_key:
            logger.warning(f"Slug de catégorie inconnu demandé pour la liste: {category_slug}")
            return []

        logger.info(f"Récupération des prompts pour la catégorie '{category_display_name}' (slug: {category_slug}) pour l'utilisateur '{user_uid if user_uid else "global"}'.")
        all_formatted_prompts: List[Dict[str, Any]] = []

        # 1. Prompts Système
        try:
            system_prompts = self.prompt_data_manager.get_prompts_by_category(json_category_key)
            for prompt in system_prompts:
                all_formatted_prompts.append({
                    "id_for_url": f"system_{prompt.get('id')}",
                    "title": prompt.get("title", "Sans Titre (Système)"),
                    "is_user_prompt": False,
                    "category_display_name": category_display_name,
                    "category_slug": category_slug,
                    "description": prompt.get("content", "")[:100] + "..." if prompt.get("content") else ""
                })
        except Exception as e:
            logger.error(f"Erreur lors de la récupération des prompts système pour la catégorie '{json_category_key}': {e}", exc_info=True)

        # 2. Prompts Utilisateur (si user_uid est fourni)
        if user_uid:
            try:
                # UserPromptData.get_prompts_by_category devrait idéalement pouvoir filtrer par user_uid
                # ou alors on charge tous les prompts d'une catégorie et on filtre ici.
                # Pour l'instant, on part du principe que get_prompts_by_category retourne ceux du user OU TOUS.
                # Si UserPromptData est instancié par utilisateur, c'est bon.
                # L'implémentation actuelle de UserPromptData ne semble pas prendre user_uid dans son constructeur
                # et get_prompts_by_category ne le prend pas non plus. Cela doit être revu dans UserPromptData.
                # Pour l'instant, on va simuler un filtrage si on avait une clé user_uid dans le prompt lui-même.
                
                # Solution temporaire : On suppose que UserPromptData.get_prompts_by_category
                # retourne tous les prompts utilisateur, et qu'on doit filtrer par user_uid si cette info
                # est stockée DANS le prompt (ce qui n'est pas le cas actuellement dans la structure donnée).
                # L'alternative est que UserPromptData soit instancié PAR utilisateur.
                # Pour l'instant, on va juste les récupérer tous pour cette catégorie, en sachant que c'est à affiner.
                user_prompts = self.user_prompt_data_manager.get_prompts_by_category(json_category_key)
                for prompt in user_prompts:
                    # Idéalement, ici on filtrerait : if prompt.get('owner_uid') == user_uid:
                    all_formatted_prompts.append({
                        "id_for_url": f"user_{prompt.get('id')}",
                        "title": prompt.get("title", "Sans Titre (Utilisateur)"),
                        "is_user_prompt": True,
                        "category_display_name": category_display_name,
                        "category_slug": category_slug,
                        "description": prompt.get("content", "")[:100] + "..." if prompt.get("content") else ""
                    })
            except Exception as e:
                logger.error(f"Erreur lors de la récupération des prompts utilisateur pour la catégorie '{json_category_key}' et utilisateur '{user_uid}': {e}", exc_info=True)

        all_formatted_prompts.sort(key=lambda p: (not p["is_user_prompt"], p["title"].lower())) # Trie système d'abord, puis par titre
        logger.info(f"{len(all_formatted_prompts)} prompts trouvés pour la catégorie '{category_display_name}'.")
        return all_formatted_prompts

    def get_prompt_details(self, prompt_id_for_url: str, user_uid: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Récupère les détails complets d'un prompt (système ou utilisateur).
        Si c'est un prompt utilisateur, vérifie que l'user_uid correspond si fourni (pourrait être une future vérification de droits).
        Retourne None si le prompt n'est pas trouvé.
        """
        logger.info(f"Récupération des détails pour prompt_id_for_url: {prompt_id_for_url} (demandé par user: {user_uid})")
        prompt_data: Optional[Dict[str, Any]] = None
        is_user_prompt = False
        category_slug = None
        actual_id = ""

        if prompt_id_for_url.startswith("system_"):
            actual_id = prompt_id_for_url[len("system_"):]
            prompt_data = self.prompt_data_manager.get_prompt(actual_id)
            is_user_prompt = False
        elif prompt_id_for_url.startswith("user_"):
            actual_id = prompt_id_for_url[len("user_"):]
            prompt_data = self.user_prompt_data_manager.get_prompt(actual_id)
            is_user_prompt = True
            # Pour les prompts utilisateur, on pourrait vouloir vérifier les droits d'accès ici si user_uid est fourni
            # if user_uid and prompt_data and prompt_data.get('owner_uid') != user_uid:
            #     logger.warning(f"Accès refusé au prompt utilisateur '{actual_id}' pour l'utilisateur '{user_uid}'.")
            #     return None 
        else:
            logger.warning(f"Format d'ID de prompt non reconnu: {prompt_id_for_url}")
            return None

        if not prompt_data:
            logger.warning(f"Prompt non trouvé avec ID (effectif): {actual_id} (type: {'user' if is_user_prompt else 'system'})")
            return None

        technical_category_name = None
        if is_user_prompt:
            category_from_user_prompt = self.user_prompt_data_manager.find_category_for_prompt(actual_id)
            if category_from_user_prompt:
                 technical_category_name = category_from_user_prompt
        else: 
            category_from_system_prompt = self.prompt_data_manager.find_category_for_prompt(actual_id)
            if category_from_system_prompt:
                technical_category_name = category_from_system_prompt
        
        json_key_to_slug = {v: k for k, v in CATEGORY_SLUG_TO_JSON_KEY.items()}
        if technical_category_name:
            category_slug = json_key_to_slug.get(technical_category_name)

        if not category_slug and technical_category_name:
             logger.warning(f"Impossible de trouver un slug pour la catégorie technique '{technical_category_name}' du prompt '{prompt_id_for_url}'.")
             category_slug = technical_category_name.lower().replace(" ", "-") 
        elif not technical_category_name:
            logger.warning(f"Catégorie technique non trouvée pour le prompt '{prompt_id_for_url}'.")

        variables_with_labels = []
        prompt_variables = prompt_data.get("variables", [])
        if prompt_data.get("needs_cockpit", False) and prompt_variables:
            for var_name in prompt_variables:
                variables_with_labels.append({
                    "name": var_name,
                    "label": self.cockpit_helper.get_field_label(var_name) 
                })
        elif prompt_variables:
             for var_name in prompt_variables:
                variables_with_labels.append({
                    "name": var_name,
                    "label": var_name 
                })

        detailed_prompt = {
            "id_for_url": prompt_id_for_url,
            "actual_id": actual_id,
            "title": prompt_data.get("title", "Sans Titre"),
            "content": prompt_data.get("content", ""),
            "welcome_page_html": prompt_data.get("welcome_page_html", ""),
            "variables_definition": variables_with_labels, 
            "accepts_files": prompt_data.get("accepts_files", False),
            "needs_cockpit": prompt_data.get("needs_cockpit", False),
            "is_user_prompt": is_user_prompt,
            "category_slug": category_slug, 
            "category_display_name": CATEGORY_SLUG_TO_DISPLAY_NAME.get(category_slug, category_slug) if category_slug else "Inconnue"
        }
        logger.debug(f"Détails du prompt '{prompt_id_for_url}' préparés: {detailed_prompt}")
        return detailed_prompt

# Instance unique du service pour un accès facile via Depends, si souhaité.
# prompt_service = PromptService()
# Alternativement, créer une fonction get_prompt_service comme pour les autres dépendances.
_prompt_service_instance: Optional[PromptService] = None

def get_prompt_service() -> PromptService:
    global _prompt_service_instance
    if _prompt_service_instance is None:
        _prompt_service_instance = PromptService()
    return _prompt_service_instance 