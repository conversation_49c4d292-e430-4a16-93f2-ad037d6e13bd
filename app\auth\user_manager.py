"""
Gestionnaire des utilisateurs autorisés et de leurs rôles via une base de données SQLite.
Intègre la notion d'administrateurs permanents définis dans la configuration.
"""

import sqlite3
import logging
import os
from typing import List, Dict, Any, Optional, Tuple, Generator, Set
from contextlib import contextmanager
from datetime import datetime, timezone
from pathlib import Path

# Utiliser le config_manager global de l'application FastAPI
from app.core.config import config_manager, PROJECT_ROOT_DIR 

logger = logging.getLogger(__name__)

DEFAULT_DB_NAME = "user_auth.db"

class UserManager:
    """Gère la base de données des utilisateurs autorisés et leurs rôles."""

    def __init__(self, db_name: Optional[str] = None):
        """
        Initialise le gestionnaire, la connexion à la base de données,
        et charge les administrateurs initiaux depuis la configuration.

        Args:
            db_name (str, optional): Nom du fichier de base de données SQLite.
                                     Utilise la valeur de config.ini ou DEFAULT_DB_NAME.
                                     Le fichier sera localisé à la racine du projet.
        """
        # Lire le nom/chemin depuis la config si non fourni explicitement
        # Le config_manager peut retourner un chemin absolu ou relatif pour user_auth_db_path
        # S'il est relatif, on le considère par rapport à PROJECT_ROOT_DIR
        db_path_config = config_manager.get_user_auth_db_path() # Devrait être une méthode spécifique dans config_manager

        if db_path_config:
            path_obj = Path(db_path_config)
            if path_obj.is_absolute():
                self.db_path = str(path_obj)
            else:
                self.db_path = str(PROJECT_ROOT_DIR / db_path_config)
        else:
            # Fallback si user_auth_db_path n'est pas dans config ou est vide
            self.db_path = str(PROJECT_ROOT_DIR / (db_name or DEFAULT_DB_NAME))
        
        self._conn = None
        self.initial_admin_uids: Set[str] = set(config_manager.get_initial_admin_uids())
        
        logger.info(f"Initialisation UserManager avec la base de données : {self.db_path}")
        if self.initial_admin_uids:
             logger.info(f"Administrateurs initiaux/permanents depuis config: {self.initial_admin_uids}")
        else:
             logger.warning("Aucun administrateur initial/permanent défini dans [Security] initial_admin_uids.")

        self._ensure_connection()
        self.create_table()

    def _ensure_connection(self):
        """Établit la connexion à la base de données si elle n'est pas active."""
        if self._conn is None:
            try:
                db_dir = os.path.dirname(self.db_path)
                if db_dir and not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)
                    logger.info(f"Répertoire créé pour la base de données : {db_dir}")

                self._conn = sqlite3.connect(self.db_path, check_same_thread=False, timeout=10)
                self._conn.row_factory = sqlite3.Row
                logger.debug("Connexion SQLite établie.")
            except sqlite3.Error as e:
                logger.error(f"Erreur de connexion à la base de données SQLite ({self.db_path}): {e}", exc_info=True)
                raise

    @contextmanager
    def _get_cursor(self) -> Generator[sqlite3.Cursor, None, None]:
        """Fournit un curseur de base de données géré par un context manager."""
        self._ensure_connection()
        if not self._conn:
             raise sqlite3.Error("La connexion à la base de données n'est pas établie.")
        cursor = None
        try:
            cursor = self._conn.cursor()
            yield cursor
            self._conn.commit()
            logger.debug("Transaction SQLite commitée.")
        except sqlite3.Error as e:
            logger.error(f"Erreur de transaction SQLite: {e}", exc_info=True)
            if self._conn:
                self._conn.rollback()
                logger.warning("Transaction SQLite annulée (rollback).")
            raise
        finally:
            if cursor:
                try:
                     cursor.close()
                except sqlite3.Error as close_err:
                     logger.error(f"Erreur lors de la fermeture du curseur SQLite: {close_err}", exc_info=True)

    def create_table(self):
        """Crée la table 'authorized_users' si elle n'existe pas."""
        sql_create_users_table = """
        CREATE TABLE IF NOT EXISTS authorized_users (
            uid TEXT PRIMARY KEY NOT NULL,
            is_admin BOOLEAN NOT NULL DEFAULT 0,
            added_by TEXT,
            added_date TEXT NOT NULL
        );
        """
        try:
            with self._get_cursor() as cursor:
                cursor.execute(sql_create_users_table)
            logger.info("Table 'authorized_users' vérifiée/créée.")
        except sqlite3.Error as e:
            logger.error(f"Erreur lors de la création de la table 'authorized_users': {e}", exc_info=True)

    def check_authorization(self, uid: str) -> Tuple[bool, Dict[str, Any]]:
        """Vérifie si l'UID est autorisé et retourne ses détails."""
        uid_lower = uid.lower()

        # Si LDAP est désactivé, autoriser le developer_uid défini dans la config
        if not config_manager.is_ldap_enabled():
            developer_uid = config_manager.get_developer_uid()
            logger.info(f"LDAP est désactivé. Tentative d'authentification avec uid={uid}, developer_uid={developer_uid}")
            
            # En mode développeur, accepter n'importe quel identifiant
            logger.info(f"Mode développeur activé. Autorisation accordée pour l'identifiant: {uid}")
            return True, {"uid": uid, "name": "Developer", "is_admin": True, "is_config_admin": False}

        if not uid:
            return False, None

        if uid in self.initial_admin_uids:
             logger.info(f"Autorisation vérifiée pour UID '{uid}': Autorisé (Admin Config).")
             return True, {"uid": uid, "is_admin": True, "is_config_admin": True}

        try:
            with self._get_cursor() as cursor:
                cursor.execute("SELECT is_admin FROM authorized_users WHERE uid = ?", (uid,))
                user_row = cursor.fetchone()
                if user_row:
                    is_admin_bool = bool(user_row["is_admin"])
                    logger.info(f"Autorisation vérifiée pour UID '{uid}': Autorisé (Admin DB: {is_admin_bool}).")
                    return True, {"uid": uid, "is_admin": is_admin_bool, "is_config_admin": False}
                else:
                    logger.warning(f"Autorisation vérifiée pour UID '{uid}': Non autorisé (pas dans DB ni config).")
                    return False, None
        except sqlite3.Error as e:
            logger.error(f"Erreur DB lors de la vérification d'autorisation pour '{uid}': {e}", exc_info=True)
            return False, None

    def add_user(self, uid: str, added_by: str, is_admin: bool = False) -> bool:
        """
        Ajoute un nouvel utilisateur autorisé dans la DB.
        Ignore si l'utilisateur est déjà un admin de config.

        Args:
            uid (str): UID du nouvel utilisateur.
            added_by (str): UID de l'admin qui ajoute l'utilisateur.
            is_admin (bool): Statut admin initial (False par défaut).

        Returns:
            bool: True si l'ajout a réussi, False sinon (ex: utilisateur existe déjà).
        """
        if not uid or not added_by:
            logger.error("Tentative d'ajout d'utilisateur avec UID ou added_by vide.")
            return False

        if uid in self.initial_admin_uids:
             logger.warning(f"Tentative d'ajout de l'admin config '{uid}' à la DB. Ignoré.")
             return False 

        now_iso = datetime.now(timezone.utc).isoformat()
        try:
            with self._get_cursor() as cursor:
                cursor.execute(
                    "INSERT INTO authorized_users (uid, is_admin, added_by, added_date) VALUES (?, ?, ?, ?)",
                    (uid, is_admin, added_by, now_iso)
                )
            logger.info(f"Utilisateur '{uid}' ajouté à la DB par '{added_by}' (Admin: {is_admin}).")
            return True
        except sqlite3.IntegrityError:
            logger.warning(f"Tentative d'ajout d'un utilisateur déjà présent dans la DB: '{uid}'.")
            return False
        except sqlite3.Error as e:
            logger.error(f"Erreur DB lors de l'ajout de l'utilisateur '{uid}': {e}", exc_info=True)
            return False

    def _count_db_admins(self, cursor: sqlite3.Cursor) -> int:
         """Compte le nombre d'administrateurs DANS LA BASE DE DONNÉES."""
         cursor.execute("SELECT COUNT(*) FROM authorized_users WHERE is_admin = 1")
         count = cursor.fetchone()[0]
         return count if count is not None else 0

    def _get_total_admin_count(self, cursor: sqlite3.Cursor) -> int:
        """ Compte le nombre total d'administrateurs (Config + DB). """
        db_admins = set()
        cursor.execute("SELECT uid FROM authorized_users WHERE is_admin = 1")
        rows = cursor.fetchall()
        if rows:
            db_admins = {row['uid'] for row in rows}
        total_admins = self.initial_admin_uids.union(db_admins)
        return len(total_admins)

    def remove_user(self, uid_to_remove: str, removed_by: str) -> bool:
        """
        Supprime un utilisateur autorisé DE LA BASE DE DONNÉES.
        Empêche la suppression d'un admin de config ou du dernier admin total.

        Args:
            uid_to_remove (str): UID de l'utilisateur à supprimer.
            removed_by (str): UID de l'admin effectuant la suppression.

        Returns:
            bool: True si la suppression réussit, False sinon.
        """
        if not uid_to_remove or not removed_by:
             logger.error("Tentative de suppression d'utilisateur avec UID ou removed_by vide.")
             return False

        if uid_to_remove in self.initial_admin_uids:
             logger.error(f"Tentative de suppression de l'admin de config '{uid_to_remove}' par '{removed_by}'. Action refusée.")
             return False

        try:
            with self._get_cursor() as cursor:
                cursor.execute("SELECT is_admin FROM authorized_users WHERE uid = ?", (uid_to_remove,))
                user_row = cursor.fetchone()
                is_db_admin = user_row and bool(user_row['is_admin'])

                if is_db_admin:
                     total_admin_count = self._get_total_admin_count(cursor)
                     if total_admin_count <= 1:
                          logger.error(f"Tentative de suppression du dernier administrateur ('{uid_to_remove}') par '{removed_by}'. Action refusée.")
                          return False

                cursor.execute("DELETE FROM authorized_users WHERE uid = ?", (uid_to_remove,))
                if cursor.rowcount > 0:
                    logger.info(f"Utilisateur '{uid_to_remove}' supprimé de la DB par '{removed_by}'.")
                    return True
                else:
                    logger.warning(f"Tentative de suppression d'un utilisateur non trouvé dans la DB: '{uid_to_remove}'.")
                    return False
        except sqlite3.Error as e:
            logger.error(f"Erreur DB lors de la suppression de l'utilisateur '{uid_to_remove}': {e}", exc_info=True)
            return False

    def set_admin_status(self, uid_to_modify: str, is_admin: bool, modified_by: str) -> bool:
        """
        Définit le statut admin d'un utilisateur DANS LA BASE DE DONNÉES.
        Empêche la rétrogradation d'un admin de config ou du dernier admin total.

        Args:
            uid_to_modify (str): UID de l'utilisateur à modifier.
            is_admin (bool): Nouveau statut admin (True ou False).
            modified_by (str): UID de l'admin effectuant la modification.

        Returns:
            bool: True si la modification réussit, False sinon.
        """
        if not uid_to_modify or not modified_by:
             logger.error("Tentative de modification de statut admin avec UID ou modified_by vide.")
             return False

        if uid_to_modify in self.initial_admin_uids:
             logger.error(f"Tentative de modification du statut de l'admin de config '{uid_to_modify}' par '{modified_by}'. Action refusée.")
             return False

        try:
            with self._get_cursor() as cursor:
                 if not is_admin:
                      cursor.execute("SELECT is_admin FROM authorized_users WHERE uid = ?", (uid_to_modify,))
                      user_row = cursor.fetchone()
                      if user_row and bool(user_row['is_admin']):
                            total_admin_count = self._get_total_admin_count(cursor)
                            if total_admin_count <= 1:
                                 logger.error(f"Tentative de retrait du statut admin du dernier administrateur ('{uid_to_modify}') par '{modified_by}'. Action refusée.")
                                 return False

                 cursor.execute("UPDATE authorized_users SET is_admin = ? WHERE uid = ?", (is_admin, uid_to_modify))
                 if cursor.rowcount > 0:
                     logger.info(f"Statut admin de '{uid_to_modify}' dans la DB mis à jour à '{is_admin}' par '{modified_by}'.")
                     return True
                 else:
                     logger.warning(f"Tentative de modification de statut admin pour un utilisateur non trouvé dans la DB: '{uid_to_modify}'.")
                     return False
        except sqlite3.Error as e:
            logger.error(f"Erreur DB lors de la modification du statut admin pour '{uid_to_modify}': {e}", exc_info=True)
            return False

    def get_all_users(self) -> List[Dict[str, Any]]:
        """
        Récupère tous les utilisateurs (Config Admins + DB Users).
        Marque les admins de config.
        """
        users_map: Dict[str, Dict[str, Any]] = {}

        for uid in self.initial_admin_uids:
             users_map[uid] = {
                  "uid": uid,
                  "is_admin": True,
                  "is_config_admin": True,
                  "added_by": "Config",
                  "added_date": None
             }

        try:
            with self._get_cursor() as cursor:
                cursor.execute("SELECT uid, is_admin, added_by, added_date FROM authorized_users ORDER BY uid ASC")
                db_users = cursor.fetchall()
                for row in db_users:
                     db_uid = row["uid"]
                     if db_uid in users_map:
                          users_map[db_uid].update({
                               "added_by": row["added_by"],
                               "added_date": row["added_date"]
                          })
                     else:
                          users_map[db_uid] = {
                               "uid": db_uid,
                               "is_admin": bool(row["is_admin"]),
                               "is_config_admin": False,
                               "added_by": row["added_by"],
                               "added_date": row["added_date"]
                          }
            all_users = sorted(list(users_map.values()), key=lambda x: x['uid'])
            return all_users
        except sqlite3.Error as e:
            logger.error(f"Erreur DB lors de la récupération de tous les utilisateurs: {e}", exc_info=True)
            return sorted(list(users_map.values()), key=lambda x: x['uid'])


    def close_connection(self):
        """Ferme la connexion à la base de données si elle est ouverte."""
        if self._conn:
            try:
                self._conn.close()
                self._conn = None
                logger.info("Connexion SQLite fermée.")
            except sqlite3.Error as e:
                logger.error(f"Erreur lors de la fermeture de la connexion SQLite: {e}", exc_info=True)

    def __del__(self):
        """Assure la fermeture de la connexion lors de la destruction de l'objet."""
        self.close_connection()

# Instance unique pour un accès facile si nécessaire (comme dans l'original)
# user_manager = UserManager() # Décommenter si besoin d'une instance globale directe
# Pour FastAPI, l'injection de dépendances est souvent préférée.
# Nous allons créer l'instance dans main.py ou via une dépendance. 