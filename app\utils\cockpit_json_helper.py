"""
Helper pour traiter et extraire des informations spécifiques des données JSON de Cockpit.
"""

import logging
import re # Ajout de re pour la méthode read_json_file si on la garde ici
from typing import Dict, List, Any, Optional, Tuple # Ajout de Tuple

logger = logging.getLogger(__name__)

class CockpitJsonHelper:
    """
    Classe helper pour analyser et extraire des informations des données JSON Cockpit.
    Cette classe ne fait pas d'appels API, elle travaille sur un dictionnaire de données fourni.
    """

    # Dictionnaire des étiquettes conviviales pour les champs Cockpit (repris de JsonProcessor)
    COCKPIT_FIELD_LABELS = {
        # Champs directs de l'affaire (issue)
        "id": "Numéro de dossier Cockpit",
        "subject": "Sujet du dossier",
        "description": "Description du dossier",
        "start_date": "Date de début",
        "due_date": "Date de fin",
        "amount": "Montant du contrat",
        "edf_currency_code": "Devi<PERSON>",
        # Champs imbriqués (syntaxe avec '.')
        "project.name": "Projet Cockpit",
        "tracker.name": "Type de dossier",
        "status.name": "Statut du dossier",
        # Champs personnalisés (via slugs)
        "type-marche": "Type de marché",
        "mode-remuneration": "Mode de rémunération",
        "montant-engagement": "Montant d'engagement",
        "type-variation-prix": "Variation des prix",
        "date-envoi-signature-fournisseur": "Date d'envoi en signature fournisseur",
        "contexte-enjeux": "Contexte et Enjeux",
        # Champs extraits/calculés
        "description_and_context": "Description et Contexte",
        "product_summary": "Produits (Synthèse)",
        "segment_summary": "Segments (Synthèse)",
        "suppliers": "Fournisseurs (Liste détaillée)",
        "suppliers_count": "Nombre de fournisseurs",
        "supplier_names": "Fournisseurs (Noms)",
        "supplier_sirets": "Fournisseurs (SIRETs)",
        "clients": "Clients / Entités bénéficiaires",
        "purchase_categories": "Catégories d'achat",
        "articles": "Articles (Désignation courte)",
        "projects": "Projets associés (EDF)"
    }

    def __init__(self, cockpit_data: Optional[Dict[str, Any]] = None):
        """
        Initialise le helper avec les données JSON Cockpit (format dict Python).

        Args:
            cockpit_data (Optional[Dict[str, Any]]): Les données JSON de Cockpit sous forme de dictionnaire.
        """
        self.data = cockpit_data if cockpit_data else {}
        self.issue_data = self.data.get("issue", {}) # Raccourci vers la section 'issue'

    def set_data(self, cockpit_data: Dict[str, Any]):
        """
        Met à jour les données Cockpit à traiter par l'instance du helper.
        """
        self.data = cockpit_data if cockpit_data else {}
        self.issue_data = self.data.get("issue", {})
        logger.debug("Données Cockpit mises à jour dans le helper.")

    def get_field_label(self, technical_name: str) -> str:
        return self.COCKPIT_FIELD_LABELS.get(technical_name, technical_name)

    def get_possible_cockpit_fields(self) -> List[str]:
        return list(self.COCKPIT_FIELD_LABELS.keys())

    def _get_custom_field_value(self, slug: str) -> Optional[Any]:
        """Helper pour récupérer la valeur d'un champ personnalisé par son slug."""
        for field in self.issue_data.get("custom_fields", []):
            if field.get("slug") == slug:
                return field.get("value")
        return None

    def extract_issue_data(self) -> Dict[str, str]:
        if not self.issue_data:
            logger.warning("Aucune donnée 'issue' à traiter dans CockpitJsonHelper.")
            return {"subject": "", "description_and_context": ""}

        subject = self.issue_data.get("subject", "")
        description = self.issue_data.get("description", "")
        
        context_and_issues = description
        contexte_enjeux_value = self._get_custom_field_value("contexte-enjeux")
        if contexte_enjeux_value:
            context_and_issues += f"\n\n{contexte_enjeux_value}"

        extracted = {
            "subject": subject,
            "description_and_context": context_and_issues.strip()
        }
        logger.debug(f"Données d'affaire extraites: {extracted}")
        return extracted

    def extract_market_data(self) -> Dict[str, str]:
        if not self.issue_data:
            logger.warning("Aucune donnée 'issue' à traiter.")
            return {"product_summary": "", "segment_summary": ""}

        long_designations = []
        domain_subset_pairs = []

        for article in self.issue_data.get("edf_articles", []):
            if article.get("long_designation"):
                long_designations.append(article.get("long_designation"))
            if article.get("domain") and article.get("subset"):
                domain_subset_pairs.append(f"{article.get('domain')} / {article.get('subset')}")
        
        product = " et ".join(list(set(long_designations)))
        segment = " et ".join(list(set(domain_subset_pairs)))

        extracted = {"product_summary": product, "segment_summary": segment}
        logger.debug(f"Données de marché extraites: {extracted}")
        return extracted

    def extract_suppliers_data(self) -> Dict[str, Any]:
        if not self.issue_data:
            logger.warning("Aucune donnée 'issue' à traiter.")
            return {"suppliers": [], "suppliers_count": 0}

        supplier_statuses = { 
            sup.get("supplier_id"): sup.get("status", "") 
            for sup in self.issue_data.get("edf_issue_suppliers", []) if sup.get("supplier_id")
        }
        
        suppliers_list = []
        for supplier in self.issue_data.get("edf_suppliers", []):
            supplier_id = supplier.get("id")
            if supplier_id in supplier_statuses:
                suppliers_list.append({
                    "name": supplier.get("name", ""),
                    "status": supplier_statuses[supplier_id],
                    "siret": supplier.get("siret"),
                    "country": supplier.get("country"),
                    "city": supplier.get("city"),
                })
        extracted = {"suppliers": suppliers_list, "suppliers_count": len(suppliers_list)}
        logger.debug(f"Données fournisseurs extraites: {extracted['suppliers_count']} fournisseurs.")
        return extracted

    def generate_key_summary_markdown(self) -> str:
        if not self.issue_data:
            logger.warning("Aucune donnée 'issue' pour générer le résumé Markdown.")
            return "Données Cockpit non disponibles ou vides."

        issue_id = self.issue_data.get("id", "N/A")
        md_summary = [f"**Résumé du dossier d'achat Cockpit {issue_id}**\n"]

        def _add_section(title_key: str, items: Dict[str, Any]):
            md_summary.append(f"**{self.get_field_label(title_key)}**")
            for key, value in items.items():
                label = self.get_field_label(key)
                val_str = str(value) if value is not None else "N/A"
                md_summary.append(f"- **{label}** : {val_str}")
            md_summary.append("") # Saut de ligne après la section

        def _add_list_section(title_key: str, item_list: List[str]):
            if item_list:
                md_summary.append(f"**{self.get_field_label(title_key)}**")
                for item in sorted(list(set(item_list))): # Tri et unicité pour la présentation
                    md_summary.append(f"- {item}")
                md_summary.append("")

        # Informations générales
        _add_section("Informations Générales", {
            "id": issue_id,
            "project.name": self.issue_data.get("project", {}).get("name", "N/A"),
            "tracker.name": self.issue_data.get("tracker", {}).get("name", "N/A"),
            "status.name": self.issue_data.get("status", {}).get("name", "N/A"),
            "subject": self.issue_data.get("subject", "N/A")
        })

        # Détails du contrat
        currency = self.issue_data.get("edf_currency_code", "EUR")
        _add_section("Détails du contrat", {
            "type-marche": self._get_custom_field_value("type-marche") or "N/A",
            "mode-remuneration": self._get_custom_field_value("mode-remuneration") or "N/A",
            "amount": f"{self.issue_data.get('amount', 'N/A')} {currency}",
            "montant-engagement": f"{self._get_custom_field_value('montant-engagement') or 'N/A'} {currency}",
            "type-variation-prix": self._get_custom_field_value("type-variation-prix") or "N/A"
        })

        # Dates
        _add_section("Dates", {
            "start_date": self.issue_data.get("start_date", "N/A"),
            "due_date": self.issue_data.get("due_date", "N/A"),
            "date-envoi-signature-fournisseur": self._get_custom_field_value("date-envoi-signature-fournisseur") or "N/A"
        })
        
        # Fournisseurs (noms et SIRETs séparément pour le résumé)
        suppliers_info = self.extract_suppliers_data().get("suppliers", [])
        supplier_names = [s.get("name") for s in suppliers_info if s.get("name")]
        supplier_sirets = [s.get("siret") for s in suppliers_info if s.get("siret")]
        _add_section("Fournisseurs", {
            "supplier_names": ", ".join(supplier_names) if supplier_names else "N/A",
            "supplier_sirets": ", ".join(supplier_sirets) if supplier_sirets else "N/A"
        })

        # Clients
        clients = [c.get("name") for c in self.issue_data.get("edf_clients", []) if c.get("name")]
        _add_list_section("clients", clients)

        # Catégories d'achat
        purchase_categories = []
        for cat_data in self.issue_data.get("edf_purchase_categories", []):
            name, code = cat_data.get("name"), cat_data.get("code")
            purchase_categories.append(f"{name} ({code})" if name and code else name or code or "N/A")
        _add_list_section("purchase_categories", purchase_categories)

        # Articles
        articles = [a.get("short_designation") for a in self.issue_data.get("edf_articles", []) if a.get("short_designation")]
        _add_list_section("articles", articles)

        # Projets associés
        projects = [p.get("name") for p in self.issue_data.get("edf_projects", []) if p.get("name")]
        _add_list_section("projects", projects)
        
        # Description
        description_value = self.issue_data.get("description")
        if description_value:
            md_summary.append(f"**{self.get_field_label('description')}**")
            md_summary.append(description_value)
            md_summary.append("")

        logger.debug("Résumé Markdown des informations clés généré.")
        return "\n".join(md_summary).strip()

    def extract_all_key_fields_for_prompt(self) -> Dict[str, Any]:
        """
        Extrait tous les champs clés des données Cockpit dans un format plat,
        prêt à être utilisé comme variables dans un template de prompt.
        """
        if not self.issue_data:
            logger.warning("Aucune donnée 'issue' pour extraire les champs clés.")
            return {}

        all_fields = {}

        # Champs directs de 'issue'
        for key in ["id", "subject", "description", "start_date", "due_date", "amount", "edf_currency_code"]:
            all_fields[key] = self.issue_data.get(key)

        # Champs de premier niveau comme project.name, tracker.name, status.name
        for nested_key in ["project", "tracker", "status"]:
            if nested_key in self.issue_data and isinstance(self.issue_data[nested_key], dict):
                all_fields[f"{nested_key}.name"] = self.issue_data[nested_key].get("name")

        # Champs personnalisés
        for cf in self.issue_data.get("custom_fields", []):
            if cf.get("slug"):
                all_fields[cf.get("slug")] = cf.get("value")

        # Données de marché (produits, segments)
        market_data = self.extract_market_data()
        all_fields.update(market_data) # product_summary, segment_summary

        # Données fournisseurs
        suppliers_data = self.extract_suppliers_data()
        all_fields["suppliers"] = suppliers_data.get("suppliers", []) # Liste de dicts détaillés
        all_fields["suppliers_count"] = suppliers_data.get("suppliers_count", 0)
        all_fields["supplier_names"] = [s.get("name") for s in all_fields["suppliers"] if s.get("name")]
        all_fields["supplier_sirets"] = [s.get("siret") for s in all_fields["suppliers"] if s.get("siret")]

        # Clients, Catégories d'achat, Articles, Projets (listes de chaînes)
        all_fields["clients"] = [c.get("name") for c in self.issue_data.get("edf_clients", []) if c.get("name")]
        
        purchase_categories_list = []
        for cat_data in self.issue_data.get("edf_purchase_categories", []):
            name, code = cat_data.get("name"), cat_data.get("code")
            purchase_categories_list.append(f"{name} ({code})" if name and code else name or code or "")
        all_fields["purchase_categories"] = [pc for pc in purchase_categories_list if pc] # Filtrer les vides

        all_fields["articles"] = [a.get("short_designation") for a in self.issue_data.get("edf_articles", []) if a.get("short_designation")]
        all_fields["projects"] = [p.get("name") for p in self.issue_data.get("edf_projects", []) if p.get("name")]

        # Description combinée
        issue_desc_data = self.extract_issue_data()
        all_fields["description_and_context"] = issue_desc_data.get("description_and_context", "")
        
        # Filtrer les valeurs None pour éviter des problèmes dans les templates, les remplacer par des chaînes vides ou valeur appropriée
        for key, value in all_fields.items():
            if value is None:
                all_fields[key] = "" # ou une autre valeur par défaut appropriée (ex: [] pour les listes)
            elif isinstance(value, list):
                 all_fields[key] = [item if item is not None else "" for item in value]


        logger.debug(f"Nombre total de champs clés extraits pour prompt: {len(all_fields)}")
        return all_fields

    # La méthode `read_json_file` de l'original JsonProcessor traitait les caractères de contrôle.
    # Cette logique n'est plus nécessaire ici car le helper reçoit des données déjà parsées (dict).
    # Si on avait besoin de lire un fichier JSON directement dans ce helper, on pourrait la remettre.

    # Les méthodes process_json_file et process_cockpit_id de l'original sont maintenant gérées par
    # la combinaison du CockpitService (pour l'appel API) et de ce Helper (pour le traitement des données). 