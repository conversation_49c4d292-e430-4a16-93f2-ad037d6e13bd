"""
Utilitaires pour la manipulation de texte.
"""

def estimate_tokens(text: str) -> int:
    """
    Estime grossièrement le nombre de tokens dans un texte.

    Basé sur l'heuristique fréquente d'environ 4 caractères par token.
    Cette estimation peut varier significativement selon le modèle de langage
    et la langue utilisée.
    """
    if not text:
        return 0
    # Utilisation de 4 comme ratio moyen caractères/token
    # On peut affiner avec une valeur spécifique si besoin (ex: 3.5 ou une bibliothèque de tokenisation)
    return int(len(text) / 4) # Assurer un entier en retour 