"""
Routes pour la gestion et l'affichage des prompts.
"""
import logging
from typing import List, Dict, Any, Literal, Optional
import asyncio
import json
import threading

from fastapi import APIRouter, Request, Depends, HTTPException, Form, UploadFile, File, BackgroundTasks
from fastapi.responses import HTMLResponse, StreamingResponse, JSONResponse
from app.main import templates

from app.auth.security import get_current_user # Import de la dépendance d'authentification
from app.services.prompt_service import get_prompt_service, PromptService, CATEGORY_SLUG_TO_DISPLAY_NAME
from app.services.cockpit_service import CockpitService # Import direct car pas de get_cockpit_service encore
from app.services.llm_service import LLMService # Import du service LLM
from app.utils.cockpit_json_helper import CockpitJsonHelper
from app.utils import file_utils # Import du module file_utils

logger = logging.getLogger(__name__)
router = APIRouter()

# Dépendance pour CockpitService (similaire à get_prompt_service)
_cockpit_service_instance: Optional[CockpitService] = None

def get_cockpit_service() -> CockpitService:
    global _cockpit_service_instance
    if _cockpit_service_instance is None:
        _cockpit_service_instance = CockpitService()
    return _cockpit_service_instance

# Dépendance pour LLMService 
_llm_service_instance: Optional[LLMService] = None

def get_llm_service() -> LLMService:
    global _llm_service_instance
    if _llm_service_instance is None:
        _llm_service_instance = LLMService()
    return _llm_service_instance

@router.get("/list/{category_slug}", response_class=HTMLResponse, name="list_prompts_for_category")
async def list_prompts_for_category(
    request: Request,
    category_slug: Literal["internal", "external"], 
    session_id: Optional[str] = None,
    current_user: Dict[str, Any] = Depends(get_current_user), # user_uid est dans current_user
    prompt_service: PromptService = Depends(get_prompt_service)
):
    user_uid = current_user.get("uid") # Récupérer l'UID pour le logging ou future logique
    
    # Log détaillé pour le débogage
    logger.info(f"Route /list/{category_slug} appelée par l'utilisateur: {user_uid}")
    logger.info(f"Session ID fourni dans l'URL: {session_id}")
    logger.info(f"Headers de la requête: {request.headers}")
    
    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans la route: {is_ajax}")
    
    # La catégorie est validée par Literal, on récupère le nom pour l'affichage
    category_display_name = CATEGORY_SLUG_TO_DISPLAY_NAME.get(category_slug, category_slug.capitalize())
    
    # Récupérer les prompts pour la catégorie
    prompts_list = prompt_service.get_prompts_for_category(category_slug, user_uid=user_uid)
    logger.info(f"Nombre de prompts trouvés pour la catégorie '{category_slug}': {len(prompts_list)}")

    # Préparer la réponse
    response = templates.TemplateResponse(
        "prompts/partials/prompt_list.html",
        {
            "request": request,
            "prompts": prompts_list,
            "category_name": category_display_name, 
            "category_slug": category_slug,
            "session_id": session_id  # Passer l'ID de session au template
        }
    )
    
    # Ajouter les en-têtes CORS
    origin = request.headers.get("Origin", "*")
    logger.info(f"Origin de la requête: {origin}")
    
    response.headers["Access-Control-Allow-Origin"] = origin
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    response.headers["Access-Control-Expose-Headers"] = "X-Login-Required, X-Login-URL"
    
    return response

@router.get("/view/{prompt_id_for_url}", response_class=HTMLResponse, name="view_prompt_detail")
async def view_prompt_detail(
    request: Request,
    prompt_id_for_url: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
    prompt_service: PromptService = Depends(get_prompt_service)
):
    user_uid = current_user.get("uid")
    logger.info(f"Affichage du détail du prompt ID URL: {prompt_id_for_url} pour l'utilisateur {user_uid}")
    
    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, user_uid)

    if not prompt_details:
        logger.warning(f"Prompt non trouvé ou accès refusé: {prompt_id_for_url}")
        raise HTTPException(status_code=404, detail=f"Prompt '{prompt_id_for_url}' non trouvé.")

    return templates.TemplateResponse(
        "prompts/prompt_detail.html",
        {
            "request": request,
            "user": current_user,
            "prompt": prompt_details,
            "category_name": prompt_details.get("category_display_name"),
            # Ajouter d'autres données nécessaires pour le template, comme les labels des variables, etc.
        }
    )

@router.post("/retrieve-cockpit/{prompt_id_for_url}", response_class=HTMLResponse, name="retrieve_cockpit_data_for_prompt")
async def retrieve_cockpit_data_for_prompt(
    request: Request,
    prompt_id_for_url: str, # Utilisé pour recharger les variables du prompt si besoin
    cockpit_id: str = Form(...),
    session_id: Optional[str] = Form(None),
    current_user: Dict[str, Any] = Depends(get_current_user),
    cockpit_service: CockpitService = Depends(get_cockpit_service),
    prompt_service: PromptService = Depends(get_prompt_service) # Pour obtenir les variables du prompt
):
    logger.info(f"Récupération des données Cockpit pour l'ID: {cockpit_id}, lié au prompt_id_url: {prompt_id_for_url}")
    logger.info(f"Session ID fourni dans le formulaire: {session_id}")

    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans retrieve_cockpit_data_for_prompt: {is_ajax}")

    if not cockpit_id.strip():
        return HTMLResponse("<div class='alert alert-warning mt-2'>Veuillez fournir un ID Cockpit.</div>", status_code=400)

    cockpit_raw_data = await cockpit_service.get_issue_data(cockpit_id)

    if cockpit_raw_data is None:
        return HTMLResponse(f"<div class='alert alert-danger mt-2'>Impossible de récupérer les données pour l'ID Cockpit '{cockpit_id}'. Vérifiez l'ID ou contactez un administrateur.</div>", status_code=404)

    cockpit_helper = CockpitJsonHelper(cockpit_raw_data)
    summary_markdown = cockpit_helper.generate_key_summary_markdown()
    all_cockpit_fields = cockpit_helper.extract_all_key_fields_for_prompt()

    # Préparer les swaps OOB pour les variables du prompt
    oob_swaps = ""
    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, current_user.get("uid"))
    if prompt_details and prompt_details.get("needs_cockpit"):
        for var_def in prompt_details.get("variables_definition", []):
            var_name = var_def.get("name")
            if var_name in all_cockpit_fields:
                field_value = all_cockpit_fields[var_name]
                # Pour les listes, on pourrait les joindre ou prendre le premier élément, selon le besoin.
                if isinstance(field_value, list):
                    field_value_str = ", ".join(map(str, field_value)) # Exemple: joindre une liste
                else:
                    field_value_str = str(field_value)
                
                # L'ID du champ input dans le template doit correspondre (ex: id="variable_NOM_VARIABLE")
                # Attention à l'échappement si les valeurs contiennent des caractères spéciaux HTML
                oob_swaps += f"<input type=\"text\" name=\"var_{var_name}\" id=\"variable_{var_name}\" value=\"{field_value_str}\" class=\"form-control\" hx-swap-oob=\"true\" />"

    # Rendre le fragment HTML du résumé et inclure les swaps OOB
    return templates.TemplateResponse(
        "prompts/partials/cockpit_summary.html",
        {
            "request": request,
            "cockpit_id_retrieved": cockpit_id,
            "summary_markdown": summary_markdown,
            "oob_swaps": oob_swaps, # Passe les swaps au template (qui peut les inclure)
            "all_cockpit_fields": all_cockpit_fields # Pour un accès direct si besoin dans le template
        }
    )

@router.post("/upload-files/{prompt_id_for_url}", response_class=HTMLResponse, name="upload_files_for_prompt")
async def upload_files_for_prompt(
    request: Request,
    prompt_id_for_url: str,
    files: List[UploadFile] = File(...), # "files" doit correspondre au name de l'input
    session_id: Optional[str] = Form(None),
    current_user: Dict[str, Any] = Depends(get_current_user),
    prompt_service: PromptService = Depends(get_prompt_service)
):
    user_uid = current_user.get("uid")
    logger.info(f"Upload de fichiers pour le prompt ID URL: {prompt_id_for_url} par l'utilisateur {user_uid}")
    logger.info(f"Session ID fourni dans le formulaire: {session_id}")
    
    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans upload_files_for_prompt: {is_ajax}")

    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, user_uid)
    if not prompt_details or not prompt_details.get("accepts_files"):
        logger.warning(f"Tentative d'upload de fichiers pour un prompt ('{prompt_id_for_url}') qui ne les accepte pas ou n'existe pas.")
        # Renvoyer un fragment d'erreur pour HTMX
        return HTMLResponse("<div class='alert alert-danger'>Ce prompt n'accepte pas de fichiers ou est invalide.</div>", status_code=400)

    uploaded_file_infos = []
    file_processing_errors = []

    session = request.session
    if "uploaded_files_info" not in session: session["uploaded_files_info"] = {}
    if prompt_id_for_url not in session["uploaded_files_info"]: session["uploaded_files_info"][prompt_id_for_url] = []
    
    # Noms des fichiers déjà en session pour ce prompt, pour éviter doublons de traitement simple
    # Une gestion plus fine des doublons (hash, etc.) serait mieux en production.
    filenames_in_session = {f_info['filename'] for f_info in session["uploaded_files_info"][prompt_id_for_url]}

    for file in files:
        if not file.filename:
            file_processing_errors.append("Un fichier sans nom a été ignoré.")
            continue
        if not file.filename.lower().endswith(".pdf") or file.content_type != "application/pdf":
            file_processing_errors.append(f"Fichier '{file.filename}' ignoré (type non PDF: {file.content_type}).")
            continue
        
        if file.filename in filenames_in_session:
            logger.info(f"Fichier '{file.filename}' est déjà en session pour ce prompt. Non re-traité.")
            continue # Ne pas ajouter de doublon simple basé sur le nom

        try:
            # Pour l'instant, on ne stocke que le nom et la taille.
            # Le contenu pourrait être stocké en session (risqué si gros) ou sur disque temporairement.
            # Ici, nous allons juste extraire le texte pour la démo et stocker cette info.
            # REMARQUE: Pour de vrais fichiers, on stockerait le fichier sur disque avec un ID unique
            # et on mettrait cet ID en session.
            
            # Lire le contenu pour obtenir la taille réelle
            # content_bytes = await file.read() # Lire une fois
            # await file.seek(0) # Rembobiner si on a besoin de relire (ex: pour file_utils.extract_text_from_pdf)

            # Exemple: Stocker en session (simplifié - ATTENTION à la taille des sessions)
            file_info = {
                "filename": file.filename,
                "content_type": file.content_type,
                "size": file.size, # Approximatif, dépend de comment FastAPI calcule ça avant .read()
                # "extracted_text_preview": text_preview # Optionnel
            }
            session["uploaded_files_info"][prompt_id_for_url].append(file_info)
            filenames_in_session.add(file.filename) # Mettre à jour pour les fichiers suivants dans la même requête
            uploaded_file_infos.append(file_info)
            logger.info(f"Fichier '{file.filename}' (type: {file.content_type}, taille: {file.size}) reçu et informations stockées en session (simulation). ")
        except Exception as e:
            logger.error(f"Erreur lors du traitement du fichier '{file.filename}': {e}", exc_info=True)
            file_processing_errors.append(f"Erreur avec '{file.filename}': {str(e)}")
        finally:
            await file.close() # Toujours fermer le fichier uploadé
    
    # Mettre à jour la session explicitement si elle a été modifiée
    # request.session est un objet qui est mis à jour, FastAPI/Starlette s'en charge.

    # Pour l'affichage, on récupère la liste complète des fichiers de la session pour ce prompt
    current_session_files = session["uploaded_files_info"][prompt_id_for_url]

    return templates.TemplateResponse(
        "prompts/partials/uploaded_files_list.html",
        {
            "request": request,
            "uploaded_files": current_session_files, # Liste des {filename, content_type, size}
            "prompt_id_for_url": prompt_id_for_url,
            "file_errors": file_processing_errors # Liste des messages d'erreur
        }
    )

@router.post("/execute/{prompt_id_for_url}", response_class=HTMLResponse, name="execute_prompt")
async def execute_prompt(
    request: Request,
    prompt_id_for_url: str,
    cockpit_id: Optional[str] = Form(None),
    temperature: Optional[float] = Form(0.7),
    session_id: Optional[str] = Form(None),
    current_user: Dict[str, Any] = Depends(get_current_user),
    prompt_service: PromptService = Depends(get_prompt_service),
    llm_service: LLMService = Depends(get_llm_service)
):
    """Exécute un prompt avec les variables fournies et renvoie la réponse du LLM."""
    user_uid = current_user.get("uid")
    logger.info(f"Exécution du prompt ID: {prompt_id_for_url} par l'utilisateur {user_uid}")
    logger.info(f"Session ID fourni dans le formulaire: {session_id}")
    
    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans execute_prompt: {is_ajax}")
    
    # Récupérer les détails du prompt
    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, user_uid)
    if not prompt_details:
        return HTMLResponse(
            content="<div class='alert alert-danger'>Prompt introuvable ou accès refusé.</div>",
            status_code=404
        )
    
    # Récupérer les variables depuis le formulaire
    form_data = await request.form()
    variables_values = {}
    for key, value in form_data.items():
        if key.startswith('var_'):
            var_name = key[4:]  # Enlever le préfixe 'var_'
            variables_values[var_name] = value
    
    # Récupérer les fichiers depuis la session (si le prompt les supporte)
    files_text = []
    if prompt_details.get("accepts_files") and "uploaded_files_info" in request.session:
        uploaded_files = request.session.get("uploaded_files_info", {}).get(prompt_id_for_url, [])
        for file_info in uploaded_files:
            # Dans une implémentation réelle, récupérer le contenu du fichier
            # Pour l'instant, on simule avec le nom du fichier
            files_text.append(f"Contenu du fichier: {file_info.get('filename', 'unknown')}")
    
    # Construire le prompt complet avec les variables remplacées
    prompt_content = prompt_details.get("content", "")
    for var_name, var_value in variables_values.items():
        var_placeholder = f"{{{var_name}}}"
        prompt_content = prompt_content.replace(var_placeholder, var_value)
    
    # Ajouter les textes des fichiers si nécessaire
    if files_text:
        prompt_content += "\n\n# Contenu des fichiers attachés\n\n" + "\n\n".join(files_text)
    
    # Appeler le service LLM pour obtenir la réponse
    try:
        # Utiliser la catégorie pour déterminer le backend LLM
        category = "internal" if prompt_details.get("category_slug") == "internal" else "external"
        
        # Appel non-streamé pour récupérer la réponse complète
        llm_response = await llm_service.generate_response(
            prompt_content=prompt_content,
            category=category,
            temperature=temperature
        )
        
        # Rendre la réponse dans un fragment HTML
        return templates.TemplateResponse(
            "prompts/partials/llm_response.html",
            {
                "request": request,
                "llm_response": llm_response,
                "prompt_content": prompt_content,
                "user": current_user
            }
        )
    
    except Exception as e:
        logger.error(f"Erreur lors de l'exécution du prompt: {e}", exc_info=True)
        return HTMLResponse(
            content=f"<div class='alert alert-danger'>Erreur lors de l'exécution du prompt: {str(e)}</div>",
            status_code=500
        )

@router.get("/stream/{prompt_id_for_url}", name="stream_prompt_execution")
async def stream_prompt_execution(
    request: Request,
    prompt_id_for_url: str,
    background_tasks: BackgroundTasks,
    session_id: Optional[str] = None,
    temperature: Optional[float] = 0.7,
    current_user: Dict[str, Any] = Depends(get_current_user),
    prompt_service: PromptService = Depends(get_prompt_service),
    llm_service: LLMService = Depends(get_llm_service)
):
    """Stream la réponse du LLM pour le prompt spécifié."""
    user_uid = current_user.get("uid")
    logger.info(f"Streaming du prompt ID: {prompt_id_for_url} par l'utilisateur {user_uid}")
    logger.info(f"Session ID fourni dans l'URL: {session_id}")
    
    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans stream_prompt_execution: {is_ajax}")
    
    # Récupérer les paramètres URL
    query_params = request.query_params
    
    # Récupérer les détails du prompt
    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, user_uid)
    if not prompt_details:
        return JSONResponse(
            content={"error": "Prompt introuvable ou accès refusé."},
            status_code=404
        )
    
    # Préparer les variables depuis les paramètres URL
    variables_values = {}
    for key, value in query_params.items():
        if key.startswith('var_'):
            var_name = key[4:]  # Enlever le préfixe 'var_'
            variables_values[var_name] = value
    
    temperature = float(query_params.get('temperature', temperature))
    
    # Construire le prompt avec les variables remplacées
    prompt_content = prompt_details.get("content", "")
    for var_name, var_value in variables_values.items():
        var_placeholder = f"{{{var_name}}}"
        prompt_content = prompt_content.replace(var_placeholder, var_value)
    
    # Créer un événement pour arrêter le stream si nécessaire
    stop_event = threading.Event()
    
    # Catégorie du LLM basée sur le type de prompt
    category = "internal" if prompt_details.get("category_slug") == "internal" else "external"
    
    # Utiliser le générateur async de stream_llm_response
    async def stream_generator():
        try:
            logger.info(f"Début du générateur de streaming pour prompt {prompt_id_for_url}")
            chunk_count = 0
            async for chunk in llm_service.stream_llm_response(
                prompt_content=prompt_content,
                category=category,
                temperature=temperature,
                stop_event=stop_event
            ):
                chunk_count += 1
                # Formater pour SSE
                yield f"data: {json.dumps({'text': chunk})}\n\n"
            
            logger.info(f"Streaming terminé normalement après {chunk_count} chunks. Envoi du signal de fin.")
            # Indiquer la fin du stream
            yield f"data: {json.dumps({'done': True})}\n\n"
        except Exception as e:
            logger.error(f"Erreur lors du streaming: {e}", exc_info=True)
            error_msg = {"error": str(e)}
            yield f"data: {json.dumps(error_msg)}\n\n"
            yield f"data: {json.dumps({'done': True})}\n\n"
    
    # Définir la tâche d'arrêt du stream après un timeout ou en cas d'annulation
    async def cleanup_task():
        await asyncio.sleep(300)  # 5 minutes timeout
        if not stop_event.is_set():
            logger.info(f"Timeout atteint pour le stream du prompt {prompt_id_for_url}")
            stop_event.set()
    
    background_tasks.add_task(cleanup_task)
    
    # Retourner le streaming response avec les en-têtes appropriés
    headers = {
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control"
    }
    
    return StreamingResponse(
        stream_generator(),
        media_type="text/event-stream",
        headers=headers
    )

@router.post("/preview/{prompt_id_for_url}", response_class=HTMLResponse, name="preview_prompt")
async def preview_prompt(
    request: Request,
    prompt_id_for_url: str,
    session_id: Optional[str] = Form(None),
    current_user: Dict[str, Any] = Depends(get_current_user),
    prompt_service: PromptService = Depends(get_prompt_service)
):
    """Prévisualise le prompt avec les variables remplies."""
    user_uid = current_user.get("uid")
    logger.info(f"Prévisualisation du prompt ID: {prompt_id_for_url} par l'utilisateur {user_uid}")
    logger.info(f"Session ID fourni dans le formulaire: {session_id}")
    
    # Récupérer les détails du prompt
    prompt_details = prompt_service.get_prompt_details(prompt_id_for_url, user_uid)
    if not prompt_details:
        return HTMLResponse(
            content="<div class='alert alert-danger'>Prompt introuvable ou accès refusé.</div>",
            status_code=404
        )
    
    # Récupérer les données du formulaire
    form_data = await request.form()
    
    # Récupérer les variables depuis le formulaire
    variables_values = {}
    logger.info(f"FormData reçu: {dict(form_data)}")
    for key, value in form_data.items():
        logger.info(f"Clé formulaire: '{key}' = '{value}'")
        if key.startswith('var_'):
            var_name = key[4:]  # Enlever le préfixe 'var_'
            variables_values[var_name] = value
            logger.info(f"Variable extraite: '{var_name}' = '{value}'")
    
    logger.info(f"Variables finales pour substitution: {variables_values}")
    
    # Construire le prompt avec les variables remplacées
    prompt_content = prompt_details.get("content", "")
    logger.info(f"Contenu prompt original: '{prompt_content}'")
    for var_name, var_value in variables_values.items():
        var_placeholder = f"{{{var_name}}}"
        logger.info(f"Remplacement: '{var_placeholder}' par '{var_value}'")
        prompt_content = prompt_content.replace(var_placeholder, var_value)
        logger.info(f"Contenu après remplacement: '{prompt_content}'")
    
    # Vérifier si c'est une requête AJAX
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    logger.info(f"Requête Ajax détectée dans preview_prompt: {is_ajax}")
    
    # Préparer la réponse
    response = templates.TemplateResponse(
        "prompts/partials/prompt_preview.html",
        {
            "request": request,
            "prompt_content": prompt_content,
            "variables_values": variables_values,
            "session_id": session_id  # Passer l'ID de session au template
        }
    )
    
    # Ajouter les en-têtes CORS
    origin = request.headers.get("Origin", "*")
    response.headers["Access-Control-Allow-Origin"] = origin
    response.headers["Access-Control-Allow-Credentials"] = "true"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Origin, X-Requested-With, Content-Type, Accept, Authorization"
    response.headers["Access-Control-Expose-Headers"] = "X-Login-Required, X-Login-URL"
    
    return response 