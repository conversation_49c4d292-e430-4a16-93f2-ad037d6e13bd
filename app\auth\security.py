"""
Logique de sécurité, gestion des sessions par cookies et dépendance d'authentification.
"""
import logging
import secrets
import time
from typing import Optional, Dict, Any, List

from fastapi import Request, HTTPException, Depends, Response
from fastapi.security.utils import get_authorization_scheme_param
from starlette.status import HTTP_401_UNAUTHORIZED, HTTP_403_FORBIDDEN

from app.auth.user_manager import UserManager
from app.core.config import config_manager

logger = logging.getLogger(__name__)

SESSION_COOKIE_NAME = "promptachat_session"

# Stockage des sessions en mémoire
# Format: {"session_id": {"user": user_details, "created_at": timestamp}}
active_sessions = {}

# Durée de validité d'une session en secondes (7 jours)
SESSION_EXPIRY = 7 * 24 * 60 * 60

def generate_session_id() -> str:
    """Génère un identifiant de session aléatoire"""
    return secrets.token_urlsafe(32)

def create_session(user_data: Dict[str, Any]) -> str:
    """
    Crée une nouvelle session et retourne l'identifiant
    """
    session_id = generate_session_id()
    active_sessions[session_id] = {
        "user": user_data,
        "created_at": time.time()
    }
    logger.info(f"Nouvelle session créée pour l'utilisateur '{user_data.get('uid')}' - ID: {session_id[:8]}...")
    return session_id

def get_session(session_id: str) -> Optional[Dict[str, Any]]:
    """
    Récupère les données de session associées à l'identifiant
    """
    if not session_id or session_id not in active_sessions:
        return None
    
    session_data = active_sessions[session_id]
    # Vérifier si la session n'a pas expiré
    if time.time() - session_data["created_at"] > SESSION_EXPIRY:
        logger.info(f"Session expirée: {session_id[:8]}...")
        del active_sessions[session_id]
        return None
    
    return session_data["user"]

def delete_session(session_id: str) -> None:
    """
    Supprime une session
    """
    if session_id in active_sessions:
        del active_sessions[session_id]
        logger.info(f"Session supprimée: {session_id[:8]}...")

def cleanup_sessions() -> None:
    """
    Nettoie les sessions expirées
    """
    current_time = time.time()
    expired_sessions = [sid for sid, data in active_sessions.items() if current_time - data["created_at"] > SESSION_EXPIRY]
    for session_id in expired_sessions:
        if session_id in active_sessions: # Vérifier avant de supprimer
            del active_sessions[session_id]
    
    if expired_sessions:
        logger.info(f"Nettoyage de {len(expired_sessions)} sessions expirées")

# ***************************************************************************************
# IMPORTANT: SÉCURITÉ DES COOKIES DE SESSION
# ---------------------------------------------------------------------------------------
# L'UID de l'utilisateur est actuellement stocké directement dans le cookie de session.
# CE N'EST PAS UNE PRATIQUE SÉCURISÉE POUR LA PRODUCTION CAR LE COOKIE PEUT ÊTRE MANIPULÉ.
# Pour une application en production, il est impératif de :
#   1. Signer le contenu du cookie (ex: avec la bibliothèque `itsdangerous`).
#   OU
#   2. Utiliser un identifiant de session opaque stocké dans le cookie, avec les détails
#      de la session stockés côté serveur (ex: dans une base de données Redis ou autre).
#
# Cette implémentation simplifiée est pour faciliter la mise en place initiale.
# La sécurisation des cookies devra être abordée dans une phase ultérieure.
# ***************************************************************************************

# Dépendance UserManager (pourrait être injectée plus globalement via app state ou autre)
# Pour simplifier, nous allons créer une instance ici pour l'utiliser dans get_current_user.
# Dans une application plus grande, on gérerait mieux la création/partage de cette instance.
_user_manager_instance: Optional[UserManager] = None

def get_user_manager() -> UserManager:
    global _user_manager_instance
    if _user_manager_instance is None:
        logger.info("Création d'une nouvelle instance de UserManager pour la sécurité.")
        _user_manager_instance = UserManager()
    return _user_manager_instance


async def get_current_user(request: Request, user_manager: UserManager = Depends(get_user_manager)) -> Dict[str, Any]:
    """
    Dépendance FastAPI pour obtenir l'utilisateur actuel à partir du paramètre de session dans l'URL
    ou du cookie de session.
    Lève une HTTPException 401 si non authentifié, ou 403 si non autorisé.
    """
    session_id = getattr(request.state, 'session_id', None)
    
    if not session_id:
        session_id = request.query_params.get("session_id")
    
    if not session_id and request.method == "POST":
        try:
            form_data = await request.form()
            session_id = form_data.get("session_id")
            logger.debug(f"get_current_user: Session ID trouvé dans le formulaire: {session_id}")
        except Exception as e:
            logger.debug(f"get_current_user: Erreur lors de la récupération du formulaire: {e}")
    
    if not session_id:
        session_id = request.cookies.get(SESSION_COOKIE_NAME)
    
    logger.debug(f"get_current_user: Cookies reçus: {request.cookies}")
    logger.debug(f"get_current_user: Session ID extrait: {session_id} (URL, formulaire ou cookie)")
    
    if not session_id:
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        logger.debug(f"get_current_user: Requête AJAX: {is_ajax}")
        logger.debug("Tentative d'accès sans identifiant de session.")
        exc_headers = {
            "X-Login-Required": "true",
            "X-Login-URL": str(request.app.url_path_for("login_page")),
            "Access-Control-Expose-Headers": "X-Login-Required, X-Login-URL"
        } if is_ajax else {"WWW-Authenticate": "Bearer"}
        detail = "Session non trouvée" if is_ajax else "Non authentifié"
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail=detail, headers=exc_headers)
    
    user_details = get_session(session_id)
    
    if not user_details:
        logger.warning(f"Session invalide ou expirée: {session_id[:8] if session_id else None}")
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        exc_headers = {
            "X-Login-Required": "true",
            "X-Login-URL": str(request.app.url_path_for("login_page")),
            "Access-Control-Expose-Headers": "X-Login-Required, X-Login-URL"
        } if is_ajax else {"WWW-Authenticate": "Bearer"}
        detail = "Session invalide ou expirée"
        raise HTTPException(status_code=HTTP_401_UNAUTHORIZED, detail=detail, headers=exc_headers)
    
    logger.info(f"Utilisateur authentifié via session: {user_details.get('uid')}")
    return user_details

def create_session_cookie(response: Response, session_id_value: str) -> None:
    """
    Crée et définit le cookie de session HTTPOnly.
    """
    response.set_cookie(
        key=SESSION_COOKIE_NAME,
        value=session_id_value,
        httponly=True,  # Cookie non accessible par JavaScript
        samesite="lax", # Permet l'envoi lors de navigations entre sites mais pas pour les requêtes CORS
        secure=False,   # False en dev HTTP, True en prod HTTPS
        domain=None,    # Ne pas spécifier pour cibler uniquement le domaine actuel
        path="/"        # Disponible sur tout le site
    )
    logger.info(f"Cookie de session créé (valeur: {session_id_value[:8]}...) avec samesite=lax")

def delete_session_cookie(response: Response) -> None:
    """
    Supprime le cookie de session.
    """
    response.delete_cookie(SESSION_COOKIE_NAME, path="/", domain=None)
    logger.info("Cookie de session supprimé.")

# Modèle Pydantic pour les données utilisateur (optionnel mais bonne pratique)
# from pydantic import BaseModel
# class User(BaseModel):
#     uid: str
#     is_admin: bool
#     is_config_admin: bool

# async def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
#     if not current_user.is_admin:
#         raise HTTPException(status_code=HTTP_403_FORBIDDEN, detail="Requiert les droits administrateur")
#     return current_user 