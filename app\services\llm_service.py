"""
Service pour interagir avec les différents LLM (Large Language Models).
"""
import logging
import asyncio
import threading
import time
import requests
import json 
import re
from queue import Queue, Empty
from typing import Dict, List, Any, Optional, Iterator, Callable, Generator, Tuple, Union, AsyncGenerator
from urllib.parse import urlparse

# Utilisation de AsyncOpenAI qui est basé sur httpx pour les appels asynchrones
from openai import AsyncOpenAI, APIConnectionError, APIStatusError, RateLimitError

from app.core.config import config_manager # Utiliser l'instance globale directement

logger = logging.getLogger(__name__)

class LLMService:
    """
    Service pour interagir avec différents LLM via API.
    Prend en charge les API OpenAI, Ollama et OneAPI (pour interne).
    """
    def __init__(self):
        """
        Initialise le service LLM.
        """
        self.config_manager = config_manager # Référence à l'instance globale
        
        self.internal_api_url = self.config_manager.get_internal_api_url()
        self.internal_api_key = self.config_manager.get_internal_api_key()
        
        self.one_api_enabled = self.config_manager.is_one_api_enabled()
        self.one_api_url = self.config_manager.get_one_api_url()
        self.one_api_token = self.config_manager.get_one_api_token()

        self.external_api_url = self.config_manager.get_external_api_url()
        self.external_api_key = self.config_manager.get_external_api_key()
        
        self.ollama_enabled = self.config_manager.is_ollama_enabled()
        self.ollama_url = self.config_manager.get_ollama_api_url()
        
        self.default_internal_model = self.config_manager.get_default_internal_model()
        self.default_external_model = self.config_manager.get_default_external_model()
        self.default_ollama_model = self.config_manager.get_default_ollama_model()
        self.confidentiality_check_model = self.config_manager.get_confidentiality_check_model()

        logger.info(
            f"LLMService initialisé. Modèles par défaut: "
            f"Interne='{self.default_internal_model}', "
            f"Externe='{self.default_external_model}', "
            f"Ollama='{self.default_ollama_model}', "
            f"Confidentialité='{self.confidentiality_check_model}'"
        )

        self.client_configs = {
            "internal": (self.internal_api_url, self.internal_api_key, self.default_internal_model),
            "external": (self.external_api_url, self.external_api_key, self.default_external_model),
            "ollama": (self.ollama_url, "ollama", self.default_ollama_model)
        }
        self._log_configurations()

    def _log_configurations(self) -> None:
        masked_internal_key = self.config_manager.mask_api_key(self.internal_api_key)
        masked_external_key = self.config_manager.mask_api_key(self.external_api_key)
        masked_oneapi_token = self.config_manager.mask_api_key(self.one_api_token)
        
        logger.info(f"Config API interne (Directe): URL={self.internal_api_url}, Key={masked_internal_key}, Default Model={self.default_internal_model}")
        logger.info(f"Config API interne (OneAPI): Enabled={self.one_api_enabled}, URL={self.one_api_url}, Token={masked_oneapi_token}")
        logger.info(f"Config API externe: URL={self.external_api_url}, Key={masked_external_key}, Default Model={self.default_external_model}")
        if self.ollama_enabled:
            logger.info(f"Config Ollama: Enabled={self.ollama_enabled}, URL={self.ollama_url}, Default Model={self.default_ollama_model}")
        else:
            logger.info("Ollama est désactivé.")

    def get_client_config(self, category: str, model_override: Optional[str] = None) -> Tuple[Optional[AsyncOpenAI], Optional[str]]:
        base_url, api_key, default_model_for_category = self.client_configs.get(category, (None, None, None))

        if not base_url:
            logger.error(f"Catégorie LLM inconnue: {category}")
            return None, None

        model_to_use = model_override if model_override else default_model_for_category
        
        log_msg_prefix = "Configuration client pour"
        if category == "internal" and model_to_use == self.confidentiality_check_model:
             log_msg_prefix = f"{log_msg_prefix} VÉRIFICATION DE CONFIDENTIALITÉ (catégorie {category})"
        else:
            log_msg_prefix = f"{log_msg_prefix} catégorie '{category}'"
        
        logger.info(f"{log_msg_prefix}. Modèle demandé: '{model_override or 'Défaut catégorie'}'. Modèle effectif: '{model_to_use}'.")

        if not model_to_use:
            logger.error(f"Aucun modèle (ni override, ni par défaut) trouvé pour la catégorie '{category}'. Vérifiez la configuration.")
            return None, None

        try:
            if category == "ollama" and self.ollama_enabled:
                logger.info(f"Utilisation de Ollama: URL={self.ollama_url}, Modèle={model_to_use}")
                return AsyncOpenAI(base_url=self.ollama_url, api_key="ollama"), model_to_use # api_key="ollama" est une convention pour le client OpenAI
            
            elif category == "internal" and self.one_api_enabled:
                logger.info(f"Utilisation de OneAPI (interne): URL={self.one_api_url}, Modèle={model_to_use}")
                return AsyncOpenAI(base_url=self.one_api_url, api_key=self.one_api_token), model_to_use
            
            elif category in ["internal", "external"]: # API directes OpenAI ou compatibles
                logger.info(f"Utilisation API directe pour '{category}': URL={base_url}, Modèle={model_to_use}")
                return AsyncOpenAI(base_url=base_url, api_key=api_key), model_to_use
            
            else:
                logger.warning(f"Configuration client non déterminée pour '{category}' (Ollama désactivé ou catégorie non gérée).")
                return None, None

        except Exception as e:
            logger.error(f"Erreur création client OpenAI pour '{category}', modèle '{model_to_use}': {e}", exc_info=True)
            return None, None

    async def get_available_models(self, category: str) -> List[str]:
        """Récupère la liste des modèles disponibles pour une catégorie."""
        if category == 'internal' and self.one_api_enabled:
            logger.warning("Récupération dynamique des modèles non supportée via OneAPI. Retour du modèle par défaut.")
            return [self.default_internal_model] if self.default_internal_model else []
        
        client, model_name = self.get_client_config(category) # Utilise le modèle par défaut pour la catégorie
        if not client:
            logger.error(f"Impossible d'obtenir un client pour la catégorie '{category}' pour lister les modèles.")
            return [model_name] if model_name else [] # Fallback au modèle par défaut si client non créé

        models_set = set()
        if model_name: # Le modèle par défaut pour cette catégorie
            models_set.add(model_name)
        
        try:
            logger.info(f"Tentative de récupération des modèles pour la catégorie '{category}' depuis {client.base_url}")
            models_list = await client.models.list()
            retrieved_models = [model.id for model in models_list.data if model.id]
            models_set.update(retrieved_models)
            logger.info(f"Modèles récupérés pour la catégorie '{category}': {retrieved_models}")
        except APIConnectionError as e:
            logger.error(f"Erreur de connexion API lors de la récupération des modèles pour '{category}' depuis {client.base_url}: {e}")
        except APIStatusError as e:
            logger.error(f"Erreur de statut API (code {e.status_code}) lors de la récupération des modèles pour '{category}': {e.message}")
        except RateLimitError as e:
            logger.error(f"Rate limit atteint lors de la récupération des modèles pour '{category}'.")
        except Exception as e:
            logger.error(f"Erreur inattendue lors de la récupération des modèles pour '{category}': {e}", exc_info=True)

        final_models = sorted(list(models_set))
        if not final_models and model_name:
            logger.warning(f"Aucun modèle récupéré pour '{category}', retour du modèle par défaut '{model_name}' uniquement.")
            return [model_name]
        if not final_models:
            logger.warning(f"Aucun modèle récupéré pour '{category}' et aucun modèle par défaut défini.")
            return []
        
        logger.info(f"Liste finale des modèles pour '{category}' (incluant défaut '{model_name}'): {final_models}")
        return final_models

    async def _execute_llm_call_stream(self, 
                                       client: AsyncOpenAI, 
                                       model_to_use: str, 
                                       prompt_content: str, 
                                       temperature: float, 
                                       stop_event: Optional[threading.Event] = None) -> AsyncGenerator[str, None]:
        """Méthode interne pour exécuter l'appel streamé et gérer les erreurs."""
        cumulative_response = ""
        try:
            logger.info(f"Appel LLM streamé: Modèle='{model_to_use}', Température={temperature:.2f}")
            response_stream = await client.chat.completions.create(
                model=model_to_use,
                messages=[{"role": "user", "content": prompt_content}],
                temperature=temperature,
                stream=True
            )
            async for chunk in response_stream:
                if stop_event and stop_event.is_set():
                    logger.info("Arrêt du stream LLM demandé par l'utilisateur.")
                    cumulative_response += "\n\n*Génération interrompue*"
                    yield cumulative_response
                    break 
                content_part = chunk.choices[0].delta.content
                if content_part:
                    cumulative_response += content_part
                    yield cumulative_response
        except APIConnectionError as e:
            logger.error(f"Erreur de connexion API LLM (stream): {e}", exc_info=True)
            yield f"{cumulative_response}\n❌ Erreur de connexion avec le LLM."            
        except APIStatusError as e:
            logger.error(f"Erreur de statut API LLM {e.status_code} (stream): {e.message}", exc_info=True)
            yield f"{cumulative_response}\n❌ Erreur API LLM ({e.status_code}). Détails: {e.message[:100]}..."
        except RateLimitError as e:
            logger.error(f"Rate limit LLM atteint (stream): {e}", exc_info=True)
            yield f"{cumulative_response}\n❌ Limite de requêtes LLM atteinte. Veuillez réessayer plus tard."
        except Exception as e:
            logger.error(f"Erreur inattendue stream LLM: {e}", exc_info=True)
            yield f"{cumulative_response}\n❌ Erreur inattendue lors de la communication avec le LLM."

    async def _execute_oneapi_call_stream(self, 
                                          model_to_use: str, 
                                          prompt_content: str, 
                                          temperature: float, 
                                          stop_event: Optional[threading.Event] = None) -> AsyncGenerator[str, None]:
        """Appel streamé spécifique pour OneAPI via requests (exécuté dans un thread)."""
        cumulative_response = ""
        q = Queue()

        def worker():
            nonlocal cumulative_response
            try:
                logger.info(f"Appel OneAPI streamé: Modèle='{model_to_use}', Température={temperature:.2f}")
                if not self.one_api_url or not self.one_api_token:
                    q.put("Erreur: Configuration OneAPI (URL ou Token) manquante.")
                    q.put(None)
                    return

                parsed_url = urlparse(self.one_api_url)
                host = parsed_url.netloc
                headers = {
                    "Authorization": f"Bearer {self.one_api_token}",
                    "Content-Type": "application/json",
                    "Host": host,
                }
                payload = {
                    "model": model_to_use,
                    "messages": [{"role": "user", "content": prompt_content}],
                    "temperature": temperature,
                    "stream": True
                }
                with requests.post(self.one_api_url, headers=headers, json=payload, stream=True, timeout=120) as response:
                    response.raise_for_status()
                    for line in response.iter_lines():
                        if stop_event and stop_event.is_set():
                            logger.info("Arrêt du stream OneAPI demandé.")
                            cumulative_response += "\n\n*Génération interrompue*"
                            q.put(cumulative_response)
                            break
                        if line:
                            decoded_line = line.decode('utf-8')
                            if decoded_line.startswith('data:'):
                                json_content = decoded_line[len('data:'):].strip()
                                if json_content == '[DONE]':
                                    break
                                try:
                                    chunk = json.loads(json_content)
                                    if isinstance(chunk, dict):
                                        choices = chunk.get('choices')
                                        if isinstance(choices, list) and choices:
                                            delta = choices[0].get('delta')
                                            if isinstance(delta, dict):
                                                content_part = delta.get('content')
                                                if isinstance(content_part, str):
                                                    cumulative_response += content_part
                                                    q.put(cumulative_response)
                                except json.JSONDecodeError:
                                    logger.warning(f"Ignoré: chunk JSON SSE OneAPI invalide: {json_content}")
                    if not (stop_event and stop_event.is_set()):
                        q.put(None) # Signal de fin normale
            except requests.exceptions.RequestException as e:
                logger.error(f"Erreur de requête OneAPI (stream): {e}", exc_info=True)
                q.put(f"{cumulative_response}\n❌ Erreur de connexion avec OneAPI: {e}")
                q.put(None)
            except Exception as e:
                logger.error(f"Erreur inattendue stream OneAPI: {e}", exc_info=True)
                q.put(f"{cumulative_response}\n❌ Erreur OneAPI: {e}")
                q.put(None)

        thread = threading.Thread(target=worker, daemon=True)
        thread.start()

        while True:
            try:
                message = q.get(timeout=0.1) # Petit timeout pour ne pas bloquer indéfiniment
                if message is None:
                    break
                yield message
            except Empty:
                if not thread.is_alive() and q.empty(): # S'assurer que le thread est mort et la queue vide
                    break
                await asyncio.sleep(0.01) # Permettre à d'autres tâches de s'exécuter
            except Exception as e:
                 logger.error(f"Erreur de la queue du stream OneAPI: {e}", exc_info=True)
                 yield "Erreur interne du stream OneAPI."
                 break
        thread.join(timeout=1.0)

    async def stream_llm_response(
        self,
        prompt_content: str,
        category: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        stop_event: Optional[threading.Event] = None # Permet l'arrêt externe
    ) -> AsyncGenerator[str, None]:
        """
        Génère une réponse du LLM en streaming, gérant différents backends.
        Le stop_event est optionnel et permet d'arrêter la génération depuis l'extérieur.
        """
        current_temperature = temperature if temperature is not None else self.config_manager.get_default_temperature()
        temp_log_display = f"{current_temperature:.2f}" if isinstance(current_temperature, float) else str(current_temperature)
        logger.info(f"Début génération streamée pour catégorie '{category}'. Modèle demandé: '{model or 'Défaut catégorie'}'. Température: {temp_log_display}")

        if category == "internal" and self.one_api_enabled:
            # Cas spécifique OneAPI (utilise requests dans un thread)
            async for chunk in self._execute_oneapi_call_stream(model_to_use=(model or self.default_internal_model),
                                                               prompt_content=prompt_content,
                                                               temperature=current_temperature,
                                                               stop_event=stop_event):
                yield chunk
        else:
            # Cas OpenAI / Ollama (utilise AsyncOpenAI)
            client, model_to_use = self.get_client_config(category, model_override=model)
            if not client or not model_to_use:
                logger.error(f"Impossible de configurer le client LLM pour '{category}' modèle '{model or 'Défaut catégorie'}'.")
                yield "❌ Erreur de configuration du client LLM. Vérifiez les logs."
                return
            
            async for chunk in self._execute_llm_call_stream(client, model_to_use, prompt_content, current_temperature, stop_event):
                yield chunk
        
        logger.info(f"Génération streamée pour '{category}' (modèle: {model or 'Défaut catégorie'}) terminée.")

    async def generate_response(
        self,
        prompt_content: str,
        category: str,
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        # files: Optional[List[Any]] = None, # Gestion des fichiers non implémentée dans cette version du client
        stop_event: Optional[threading.Event] = None
    ) -> str:
        """
        Génère une réponse complète du LLM (non-streamée pour l'appelant).
        Utilise le stream en interne et accumule la réponse.
        """
        logger.info(f"Génération de réponse complète (non-streamée) pour catégorie '{category}', modèle '{model or 'Défaut catégorie'}'.")
        full_response = ""
        async for chunk in self.stream_llm_response(prompt_content, category, model, temperature, stop_event):
            full_response = chunk # Le stream_llm_response yield déjà la réponse cumulative
        
        if stop_event and stop_event.is_set() and "*Génération interrompue*" not in full_response:
            full_response += "\n\n*Génération interrompue*"

        logger.info(f"Réponse complète générée pour '{category}', modèle '{model or 'Défaut catégorie'}'. Longueur: {len(full_response)} chars.")
        return full_response

# Instance unique du service pour un accès facile, si souhaité.
# Dépendra de la manière dont les dépendances sont gérées dans FastAPI (ex: injection).
# llm_service = LLMService() 