{# templates/prompts/partials/uploaded_files_list.html #}

{% if file_errors %}
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erreurs lors de l'upload :</strong>
        <ul>
            {% for error in file_errors %}
                <li>{{ error }}</li>
            {% endfor %}
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
{% endif %}

{% if uploaded_files %}
    <p>Fichiers téléversés pour ce prompt (session actuelle) :</p>
    <ul class="list-group list-group-flush">
        {% for file in uploaded_files %}
            <li class="list-group-item d-flex justify-content-between align-items-center">
                <span>
                    <i class="bi bi-file-earmark-pdf-fill text-danger"></i> 
                    {{ file.filename }} 
                    <small class="text-muted">({{ (file.size / 1024) | round(1) }} Ko)</small>
                </span>
                {# Optionnel: bouton pour retirer le fichier de la session #}
                {# <button class="btn btn-sm btn-outline-danger" 
                          hx-post="{{ url_for('remove_uploaded_file', prompt_id_for_url=prompt_id_for_url, filename=file.filename) }}" 
                          hx-target="#uploaded-files-list" 
                          hx-swap="innerHTML">
                    Retirer
                </button> #}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <p class="text-muted small">Aucun fichier téléversé pour ce prompt dans cette session.</p>
{% endif %} 