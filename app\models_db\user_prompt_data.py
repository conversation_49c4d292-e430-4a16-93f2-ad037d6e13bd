"""
Modèle de données pour la gestion des prompts utilisateur.
"""

import json
import logging
import shutil # Ajouté pour une éventuelle gestion de backup, bien que non utilisé dans save_prompts ici
from typing import Dict, List, Any, Optional
from pathlib import Path
import uuid

# Importer le chemin racine du projet défini dans la config
from app.core.config import PROJECT_ROOT_DIR, config_manager

logger = logging.getLogger(__name__)

# Définir le chemin complet vers user_prompts.json
# Ce fichier sera spécifique à une instance ou à un utilisateur si l'application est multi-utilisateurs
# Pour l'instant, nous le plaçons à la racine comme prompts.json.
# Une stratégie multi-utilisateurs nécessiterait une gestion des chemins différente (ex: par ID utilisateur).
DEFAULT_USER_PROMPTS_JSON_PATH = PROJECT_ROOT_DIR / "user_prompts.json"

class UserPromptData:
    """
    Classe pour gérer les données des prompts personnalisés par l'utilisateur.
    """

    def __init__(self, file_path: Optional[Path] = None):
        """
        Initialise la classe UserPromptData.

        Args:
            file_path (Path, optional): Chemin vers le fichier JSON contenant les prompts utilisateur.
                                         Si None, utilise DEFAULT_USER_PROMPTS_JSON_PATH.
        """
        self.file_path = file_path if file_path else DEFAULT_USER_PROMPTS_JSON_PATH
        self.prompts_data: Optional[Dict[str, List[Dict[str, Any]]]] = None
        self.load_prompts()

    def load_prompts(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Charge les prompts depuis le fichier JSON utilisateur.
        Si le fichier n'existe pas, crée une structure vide en mémoire.

        Returns:
            dict: Dictionnaire contenant les prompts par catégorie (copie).
        """
        try:
            if self.file_path.exists():
                with self.file_path.open("r", encoding="utf-8") as f:
                    self.prompts_data = json.load(f)
                logger.info(f"Prompts utilisateur chargés depuis {self.file_path}")
            else:
                self.prompts_data = {"internal": [], "external": []}
                logger.info(f"Fichier de prompts utilisateur '{self.file_path}' non trouvé. Initialisation avec une structure vide.")
                # La structure sera sauvegardée lors de la première modification nécessitant une sauvegarde.

            if not isinstance(self.prompts_data, dict):
                logger.error(f"Le contenu de {self.file_path} n'est pas un dictionnaire JSON valide. Initialisation vide.")
                self.prompts_data = {"internal": [], "external": []}
                return self.prompts_data.copy()

            # Vérifier la structure et les champs essentiels
            required_categories = ["internal", "external"]
            data_modified_for_consistency = False

            for category in required_categories:
                if category not in self.prompts_data or not isinstance(self.prompts_data.get(category), list):
                    self.prompts_data[category] = []
                    data_modified_for_consistency = True

            for category_name, category_list in self.prompts_data.items():
                if not isinstance(category_list, list):
                    logger.warning(f"Contenu invalide pour la catégorie utilisateur '{category_name}'. Remplacement par liste vide.")
                    self.prompts_data[category_name] = []
                    data_modified_for_consistency = True
                    continue

                valid_prompts = []
                for prompt in category_list:
                    if isinstance(prompt, dict) and prompt.get("id") and prompt.get("title") and "content" in prompt:
                        prompt.setdefault("variables", [])
                        prompt.setdefault("accepts_files", False)
                        prompt.setdefault("needs_cockpit", False)
                        prompt.setdefault("welcome_page_html", "")
                        prompt.setdefault("user_created", True) # Assurer que ce flag est là pour les prompts utilisateurs
                        valid_prompts.append(prompt)
                    else:
                        logger.warning(f"Prompt utilisateur invalide ou manquant de champs (id, title, content) dans '{category_name}', ignoré: {str(prompt)[:100]}...")
                        data_modified_for_consistency = True
                self.prompts_data[category_name] = valid_prompts
            
            # if data_modified_for_consistency:
            #     logger.info(f"Structure de '{self.file_path}' vérifiée/corrigée pour les prompts utilisateur.")
                # self.save_prompts() # Sauvegarder seulement si nécessaire et si c'est une action voulue

            return self.prompts_data.copy() # Retourner une copie

        except json.JSONDecodeError as e:
            logger.error(f"Erreur de décodage JSON du fichier de prompts utilisateur '{self.file_path}': {e}")
            self.prompts_data = {"internal": [], "external": []}
            return self.prompts_data.copy()
        except Exception as e:
            logger.error(f"Erreur inattendue lors du chargement de {self.file_path}: {e}", exc_info=True)
            self.prompts_data = {"internal": [], "external": []}
            return self.prompts_data.copy()

    def get_prompt(self, prompt_id: str) -> Optional[Dict[str, Any]]:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None: return None

        for category_list in self.prompts_data.values():
            if isinstance(category_list, list):
                for prompt in category_list:
                    if isinstance(prompt, dict) and prompt.get("id") == prompt_id:
                        return prompt.copy()
        logger.debug(f"Prompt utilisateur avec ID '{prompt_id}' non trouvé.")
        return None

    def get_prompts_by_category(self, category: str) -> List[Dict[str, Any]]:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None: return []
        
        logger.info(f"Récupération des prompts utilisateur pour la catégorie: {category}")
        logger.info(f"Catégories disponibles: {list(self.prompts_data.keys())}")
        
        prompts_list = self.prompts_data.get(category, [])
        if isinstance(prompts_list, list):
            result = [p.copy() for p in prompts_list if isinstance(p, dict)]
            logger.info(f"Nombre de prompts utilisateur trouvés pour la catégorie '{category}': {len(result)}")
            return result
            
        logger.warning(f"La catégorie '{category}' n'est pas une liste valide dans user_prompts.json")
        return []

    def find_category_for_prompt(self, prompt_id: str) -> Optional[str]:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None: return None

        for category_name, prompts_list in self.prompts_data.items():
            if isinstance(prompts_list, list):
                if any(isinstance(p, dict) and p.get("id") == prompt_id for p in prompts_list):
                    return category_name
        return None

    def _generate_unique_id(self, category: str) -> str:
        return f"user_{category}_{str(uuid.uuid4())[:8]}"

    def add_prompt(self, category: str, prompt_data: Dict[str, Any]) -> Optional[str]:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None or category not in self.prompts_data or not isinstance(self.prompts_data.get(category), list):
            logger.error(f"Catégorie utilisateur '{category}' non trouvée ou invalide pour ajout.")
            return None

        prompt_id = prompt_data.get("id")
        title = prompt_data.get("title")

        if not title:
            logger.warning(f"Titre manquant pour le nouveau prompt utilisateur. Utilisation d'un titre par défaut.")
            title = f"Prompt utilisateur sans titre {str(uuid.uuid4())[:4]}"

        if not prompt_id or self.get_prompt(prompt_id):
            old_id = prompt_id
            prompt_id = self._generate_unique_id(category)
            if old_id:
                logger.warning(f"L'ID de prompt utilisateur '{old_id}' existe déjà ou est nul. Nouvel ID généré: {prompt_id}")
            else:
                logger.info(f"Nouvel ID de prompt utilisateur généré: {prompt_id}")
        
        new_prompt = {
            "id": prompt_id,
            "title": title,
            "content": prompt_data.get("content", ""),
            "variables": prompt_data.get("variables", []),
            "accepts_files": prompt_data.get("accepts_files", False),
            "needs_cockpit": prompt_data.get("needs_cockpit", False),
            "welcome_page_html": prompt_data.get("welcome_page_html", ""),
            "user_created": True
        }

        self.prompts_data[category].append(new_prompt)
        if self.save_prompts():
            logger.info(f"Nouveau prompt utilisateur '{prompt_id}' ('{title}') ajouté à la catégorie '{category}'.")
            return prompt_id
        else:
            logger.error(f"Échec de la sauvegarde du nouveau prompt utilisateur {prompt_id}. Tentative de rollback en mémoire.")
            self.prompts_data[category] = [p for p in self.prompts_data[category] if p.get("id") != prompt_id]
            return None

    def update_prompt(self, prompt_id: str, updated_data: Dict[str, Any]) -> bool:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None: return False

        category = self.find_category_for_prompt(prompt_id)
        if not category or not isinstance(self.prompts_data.get(category), list):
            logger.error(f"Prompt utilisateur ID '{prompt_id}' non trouvé ou catégorie invalide pour mise à jour.")
            return False

        for i, prompt in enumerate(self.prompts_data[category]):
            if isinstance(prompt, dict) and prompt.get("id") == prompt_id:
                # Conserver l'ID original et le statut utilisateur, mettre à jour le reste
                current_prompt_copy = prompt.copy()
                current_prompt_copy.update(updated_data)
                current_prompt_copy["id"] = prompt_id # Assurer que l'ID n'est pas changé
                current_prompt_copy["user_created"] = True # Assurer que le flag reste
                current_prompt_copy.setdefault("welcome_page_html", "")
                current_prompt_copy.setdefault("variables", [])
                current_prompt_copy.setdefault("accepts_files", False)
                current_prompt_copy.setdefault("needs_cockpit", False)
                
                self.prompts_data[category][i] = current_prompt_copy
                if self.save_prompts():
                    logger.info(f"Prompt utilisateur '{prompt_id}' mis à jour avec succès.")
                    return True
                else:
                    logger.error(f"Échec de la sauvegarde lors de la mise à jour du prompt utilisateur '{prompt_id}'.")
                    # Rollback de la modification en mémoire (optionnel, pourrait être complexe)
                    # Pour l'instant, on considère que l'état en mémoire est toujours celui après tentative de modif.
                    return False
        
        logger.warning(f"Prompt utilisateur '{prompt_id}' non trouvé dans la catégorie '{category}' lors de la tentative de mise à jour.")
        return False

    def delete_prompt(self, prompt_id: str) -> bool:
        if self.prompts_data is None: self.load_prompts()
        if self.prompts_data is None: return False

        category = self.find_category_for_prompt(prompt_id)
        if not category or not isinstance(self.prompts_data.get(category), list):
            logger.error(f"Prompt utilisateur ID '{prompt_id}' non trouvé ou catégorie invalide pour suppression.")
            return False

        initial_length = len(self.prompts_data[category])
        self.prompts_data[category] = [p for p in self.prompts_data[category] if not (isinstance(p, dict) and p.get("id") == prompt_id)]

        if len(self.prompts_data[category]) < initial_length:
            if self.save_prompts():
                logger.info(f"Prompt utilisateur '{prompt_id}' supprimé avec succès de la catégorie '{category}'.")
                return True
            else:
                logger.error(f"Échec de la sauvegarde après suppression du prompt utilisateur '{prompt_id}'.")
                # Idéalement, restaurer le prompt en mémoire si la sauvegarde échoue
                # Pour l'instant, on ne le fait pas pour simplifier
                return False
        else:
            logger.warning(f"Prompt utilisateur '{prompt_id}' non trouvé dans la catégorie '{category}' pour suppression effective.")
            return False

    def duplicate_system_prompt(self, system_prompt: Dict[str, Any], category_key: str) -> Optional[str]:
        if not system_prompt or not isinstance(system_prompt, dict):
            logger.error("Prompt système invalide fourni pour duplication.")
            return None
        if category_key not in ["internal", "external"]:
            logger.error(f"Catégorie '{category_key}' invalide pour duplication de prompt système.")
            return None

        user_prompt_copy = system_prompt.copy()
        new_id = self._generate_unique_id(category_key)
        
        user_prompt_copy["id"] = new_id
        user_prompt_copy["title"] = f"{user_prompt_copy.get('title', 'Prompt')} (copie utilisateur)"
        user_prompt_copy["user_created"] = True
        user_prompt_copy.setdefault("welcome_page_html", "") # Assurer sa présence

        return self.add_prompt(category_key, user_prompt_copy)

    def save_prompts(self) -> bool:
        if self.prompts_data is None:
            logger.error("Tentative de sauvegarde des prompts utilisateur sans données (None). Annulation.")
            return False
        try:
            self.file_path.parent.mkdir(parents=True, exist_ok=True)
            with self.file_path.open("w", encoding="utf-8") as f:
                json.dump(self.prompts_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Prompts utilisateur sauvegardés dans {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde des prompts utilisateur dans {self.file_path}: {e}", exc_info=True)
            return False

    def change_prompts_file(self, new_file_path_str: str) -> bool:
        """
        Change le fichier de prompts utilisateur utilisé par cette instance.
        Attention: Ceci affecte uniquement CETTE INSTANCE de UserPromptData.
        """
        if not new_file_path_str:
            logger.error("Chemin de fichier vide fourni pour changer de fichier de prompts utilisateur.")
            return False
        
        new_path = Path(new_file_path_str)
        if not new_path.is_absolute():
            new_path = PROJECT_ROOT_DIR / new_path_str
            logger.info(f"Chemin relatif '{new_file_path_str}' résolu en '{new_path}'.")

        old_file_path = self.file_path
        try:
            self.file_path = new_path
            self.prompts_data = None # Forcer rechargement
            self.load_prompts()
            logger.info(f"Fichier de prompts utilisateur pour cette instance changé avec succès: {self.file_path}")
            return True
        except Exception as e:
            logger.error(f"Erreur lors du changement de fichier de prompts utilisateur vers {new_path}: {e}. Rétablissement de {old_file_path}.", exc_info=True)
            self.file_path = old_file_path
            self.prompts_data = None
            self.load_prompts()
            return False

# Instance unique (optionnel, dépend de la stratégie de gestion des utilisateurs)
# Pour une application mono-utilisateur ou si les prompts sont globaux mais modifiables.
# user_prompt_manager = UserPromptData() 