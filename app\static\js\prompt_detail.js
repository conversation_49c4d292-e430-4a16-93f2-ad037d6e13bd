/**
 * prompt_detail.js - JavaScript pour la page de détail des prompts
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('prompt_detail.js chargé');
    
    let isStreaming = false;
    let streamSource = null;
    let currentSessionId = null; 

    function getSessionIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('session_id');
    }

    currentSessionId = getSessionIdFromUrl();
    if (currentSessionId) {
        console.log('Session ID récupéré de l\'URL:', currentSessionId);
    }
    
    function isLoginPage(html) {
        return html && 
               (html.includes('Connexion') && html.includes('Mot de passe')) || 
               html.includes('L\'authentification LDAP est actuellement désactivée');
    }
    
    function redirectToLogin() {
        console.log('Page de connexion détectée, redirection...');
        
        const urlParams = new URLSearchParams(window.location.search);
        const sessionId = currentSessionId || urlParams.get('session_id'); // Utiliser currentSessionId s'il est défini
        
        let loginUrl = '/auth/login';
        let returnToPath = encodeURIComponent(window.location.pathname + window.location.search);
        
        // Construire l'URL de retour, en s'assurant que session_id n'est pas dupliqué si currentSessionId est déjà dans window.location.search
        let existingQuery = window.location.search;
        if (currentSessionId && existingQuery.includes(`session_id=${currentSessionId}`)) {
            // Si currentSessionId est déjà le session_id dans l'URL, pas besoin de l'ajouter à return_to
            // Mais il faut s'assurer que return_to lui-même est correct
        } else if (sessionId) {
             // Si on utilise un sessionId (soit de currentSessionId, soit fraîchement lu de l'URL pour être sûr)
             // et qu'il n'était pas celui qui a construit le returnToPath, on le force.
            // Ceci est un peu complexe, simplifions: on s'assure que le return_to a le session_id actuel s'il existe.
            const tempUrl = new URL(window.location.origin + window.location.pathname + window.location.search);
            if (sessionId) {
                tempUrl.searchParams.set('session_id', sessionId);
            }
            returnToPath = encodeURIComponent(tempUrl.pathname + tempUrl.search);
        }

        loginUrl += `?return_to=${returnToPath}`;
        // Si nous avons un session_id à transmettre à la page de login elle-même (ex: pour des messages)
        // on pourrait l'ajouter ici, mais généralement return_to est la clé.
        // if (sessionId) { loginUrl += `&login_session_id=${sessionId}`; }

        window.location.href = loginUrl;
        return false;
    }
    
    function initializeDisplayAreas() {
        const previewArea = document.getElementById('prompt-preview-area');
        if (previewArea) {
            if (previewArea.innerHTML.includes('Connexion') && previewArea.innerHTML.includes('Mot de passe')) {
                console.log('Mire d\'authentification détectée dans la prévisualisation, nettoyage');
                previewArea.innerHTML = '<p class="text-muted">Complétez les variables pour prévisualiser le prompt...</p>';
            } 
            else if (document.querySelector('[id^="variable_"]')?.value) {
                updatePromptPreviewLocal();
            }
            else if (!previewArea.querySelector('pre')) {
                previewArea.innerHTML = '<p class="text-muted">Complétez les variables pour prévisualiser le prompt...</p>';
            }
        }
        
        const responseArea = document.getElementById('llm-response-area');
        if (responseArea) {
            if (responseArea.innerHTML.includes('Connexion') && responseArea.innerHTML.includes('Mot de passe')) {
                console.log('Mire d\'authentification détectée dans la zone de réponse, nettoyage');
                responseArea.innerHTML = '<em class="text-muted">La réponse du LLM apparaîtra ici après exécution du prompt...</em>';
            }
        }
    }
    
    initializeDisplayAreas();
    
    const refreshButton = document.getElementById('refresh-view-btn');
    if (refreshButton) {
        refreshButton.addEventListener('click', function(event) { 
            event.stopPropagation(); 
            console.log('Rafraîchissement de la vue avec variables via appel serveur');
            updatePromptPreview(); 
        });
    }
    
    const variableInputs = document.querySelectorAll('[id^="variable_"]');
    variableInputs.forEach(input => {
        input.addEventListener('blur', function() { 
            console.log('Focus perdu sur le champ de variable, mise à jour serveur de la prévisualisation');
            updatePromptPreview(); 
        });
    });
    
    function updatePromptPreviewLocal() {
        console.log('Mise à jour locale de la prévisualisation');
        const promptContentEl = document.querySelector('#prompt-content-collapse pre code');
        if (!promptContentEl) {
            console.error('Contenu du prompt non trouvé pour la prévisualisation locale');
            return;
        }
        let previewContent = promptContentEl.textContent;
        const variablesForm = document.getElementById('variables-form');
        if (!variablesForm) return;

        variableInputs.forEach(input => {
            const varName = input.id.replace('variable_', '');
            const varValue = input.value.trim();
            if (varValue) {
                const regex = new RegExp(`{${varName}}`, 'g');
                previewContent = previewContent.replace(regex, varValue);
            }
        });
        
        const previewArea = document.getElementById('prompt-preview-area');
        if (previewArea) {
            previewArea.innerHTML = `<pre style="white-space: pre-wrap; word-wrap: break-word;">${previewContent}</pre>`;
        }
    }
    
    const previewButton = document.getElementById('preview-prompt-btn');
    if (previewButton) {
        previewButton.style.display = 'none';
    }
    
    const executeButton = document.getElementById('execute-prompt-btn');
    if (executeButton) {
        executeButton.addEventListener('click', executePrompt);
    }
    
    const streamButton = document.getElementById('stream-prompt-btn');
    if (streamButton) {
        streamButton.addEventListener('click', streamPrompt);
    }
    
    const stopStreamButton = document.getElementById('stop-stream-btn');
    if (stopStreamButton) {
        stopStreamButton.addEventListener('click', stopStreaming);
    }
    
    function updatePromptPreview() {
        console.log('updatePromptPreview appelé');
        const form = document.getElementById('variables-form');
        if (!form) {
            console.error('Formulaire variables-form non trouvé');
            return;
        }
        const promptId = form.getAttribute('data-prompt-id');
        if (!promptId) {
            console.error('Attribut data-prompt-id non trouvé');
            return;
        }
        
        // Debug: Afficher tous les éléments du formulaire
        console.log('Éléments du formulaire:');
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            console.log(`Element: ${input.tagName}, name="${input.name}", value="${input.value}", id="${input.id}"`);
        });
        
        const formData = new FormData(form);
        if (currentSessionId && !form.querySelector('input[name="session_id"]')) {
            formData.append('session_id', currentSessionId);
            console.log('Session ID global ajouté à la requête de prévisualisation:', currentSessionId);
        } else if (form.querySelector('input[name="session_id"]')) {
            console.log('Session ID déjà présent dans le formulaire (input caché) pour prévisualisation.');
        }
        
        console.log('Données du formulaire pour prévisualisation:');
        for (const [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }
        
        // Debug: Vérifier s'il y a des variables à traiter
        let hasVariables = false;
        for (const [key, value] of formData.entries()) {
            if (key.startsWith('var_') && value.trim() !== '') {
                hasVariables = true;
                break;
            }
        }
        console.log('Le formulaire contient des variables remplies:', hasVariables);
        
        document.getElementById('prompt-preview-area').innerHTML = 
            '<div class="card-body"><div class="d-flex justify-content-center"><div class="spinner-border" role="status"><span class="visually-hidden">Chargement...</span></div></div></div>';
        
        fetch(`/prompts/preview/${promptId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    console.log('Session expirée ou non authentifiée lors de la prévisualisation, redirection...');
                    redirectToLogin(); 
                    throw new Error('Session expirée');
                }
                throw new Error('Erreur HTTP: ' + response.status);
            }
            return response.text();
        })
        .then(html => {
            if (isLoginPage(html)) {
                redirectToLogin();
            } else {
                document.getElementById('prompt-preview-area').innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Erreur lors de la prévisualisation:', error);
            if (!error.message.includes('Session expirée')) { // Ne pas afficher deux fois si déjà géré
                document.getElementById('prompt-preview-area').innerHTML = 
                    '<div class="alert alert-danger">Erreur lors de la prévisualisation du prompt.</div>';
            }
        });
    }
    
    function executePrompt() {
        console.log('executePrompt appelé');
        const form = document.getElementById('variables-form');
        if (!form) {
            console.error('Formulaire variables-form non trouvé pour exécution');
            return;
        }
        const promptId = form.getAttribute('data-prompt-id');
        if (!promptId) {
            console.error('Attribut data-prompt-id non trouvé pour exécution');
            return;
        }
        
        const formData = new FormData(form);
        if (currentSessionId && !form.querySelector('input[name="session_id"]')) {
            formData.append('session_id', currentSessionId);
            console.log('Session ID global ajouté à la requête d\'exécution:', currentSessionId);
        } else if (form.querySelector('input[name="session_id"]')) {
            console.log('Session ID déjà présent dans le formulaire (input caché) pour exécution.');
        }

        console.log('Données du formulaire pour exécution:');
        for (const [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }
        
        const executeBtn = document.getElementById('execute-prompt-btn');
        executeBtn.disabled = true;
        executeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Traitement...';
        
        document.getElementById('llm-response-area').innerHTML = 
            '<div class="card-header">Réponse du LLM</div>' +
            '<div class="card-body"><div class="d-flex justify-content-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div></div></div>';
        
        fetch(`/prompts/execute/${promptId}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            credentials: 'include'
        })
        .then(response => {
            if (!response.ok) {
                if (response.status === 401) {
                    console.log('Session expirée ou non authentifiée lors de l\'exécution, redirection...');
                    redirectToLogin();
                    throw new Error('Session expirée');
                }
                throw new Error('Erreur HTTP: ' + response.status);
            }
            return response.text();
        })
        .then(html => {
            if (isLoginPage(html)) {
                redirectToLogin();
            } else {
                document.getElementById('llm-response-area').innerHTML = html;
            }
        })
        .catch(error => {
            console.error('Erreur lors de l\'exécution du prompt:', error);
            if (!error.message.includes('Session expirée')) {
                 document.getElementById('llm-response-area').innerHTML = 
                    '<div class="card-header bg-danger text-white">Erreur</div>' +
                    '<div class="card-body"><div class="alert alert-danger">Une erreur est survenue lors de l\'exécution du prompt.</div></div>';
            }
        })
        .finally(() => {
            executeBtn.disabled = false;
            executeBtn.innerHTML = 'Exécuter le Prompt'; // Adapter le texte si c'était un autre bouton
        });
    }
    
    function streamPrompt() {
        if (isStreaming) {
            console.warn('Un streaming est déjà en cours.');
            return;
        }
        
        const form = document.getElementById('variables-form');
        if (!form) { console.error('Formulaire variables-form non trouvé pour streaming'); return; }
        const promptId = form.getAttribute('data-prompt-id');
        if (!promptId) { console.error('Attribut data-prompt-id non trouvé pour streaming'); return; }

        const formData = new FormData(form);
        let streamUrl = `/prompts/stream/${promptId}`;
        const params = new URLSearchParams();

        for (const pair of formData.entries()) {
            // Ne pas ajouter les champs de fichier au query params pour EventSource
            if (!(pair[1] instanceof File)) {
                params.append(pair[0], pair[1]);
            }
        }
        
        // Ajouter une température par défaut si pas spécifiée
        if (!params.has('temperature')) {
            params.append('temperature', '0.7');
        }
        
        // Ajouter le session_id global s'il est défini et non déjà dans les params
        if (currentSessionId && !params.has('session_id') && !form.querySelector('input[name="session_id"]')) {
            params.append('session_id', currentSessionId);
            console.log('Session ID global ajouté à la requête de streaming (URL):', currentSessionId);
        } else if (params.has('session_id') || form.querySelector('input[name="session_id"]')){
            console.log('Session ID déjà présent pour le streaming.');
        }

        if (params.toString()) { 
            streamUrl += `?${params.toString()}`;
        }
        console.log('URL de streaming:', streamUrl);
        
        const streamBtn = document.getElementById('stream-prompt-btn');
        const stopStreamBtn = document.getElementById('stop-stream-btn');
        streamBtn.disabled = true;
        streamBtn.classList.add('d-none');
        stopStreamBtn.classList.remove('d-none');
        
        const responseArea = document.getElementById('llm-response-area');
        responseArea.innerHTML = 
            '<div class="card-header bg-info text-white d-flex justify-content-between align-items-center">' +
            'Réponse du LLM (streaming)' +
            '<div class="spinner-border spinner-border-sm text-white" role="status"><span class="visually-hidden">Streaming...</span></div></div>' +
            '<div class="card-body"><pre id="streaming-content" style="white-space: pre-wrap; word-wrap: break-word;"></pre></div>';
        
        streamSource = new EventSource(streamUrl, { withCredentials: true }); // Ajout de withCredentials ici aussi
        isStreaming = true;
        
        streamSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            const streamingContentEl = document.getElementById('streaming-content');

            if (data.error) {
                console.error('Erreur de streaming reçue du serveur:', data.error);
                if(streamingContentEl) streamingContentEl.innerHTML = `<span class="text-danger">Erreur: ${data.error}</span>`;
                stopStreaming(); 
            } else if (data.done) {
                console.log('Streaming terminé par le serveur.');
                // Changer le header en succès AVANT d'appeler stopStreaming
                const responseHeader = document.querySelector('#llm-response-area .card-header');
                if (responseHeader && responseHeader.classList.contains('bg-info')) {
                    responseHeader.innerHTML = 'Réponse du LLM (complète)';
                    responseHeader.classList.remove('bg-info');
                    responseHeader.classList.add('bg-success');
                }
                stopStreaming();
            } else if (data.text) {
                // Le serveur envoie déjà le texte cumulatif complet, pas besoin d'accumuler côté client
                if(streamingContentEl) streamingContentEl.innerText = data.text; // Utiliser innerText pour un affichage correct du texte brut
            }
        };
        
        streamSource.onerror = function(error) {
            console.error('Erreur EventSource:', error);
            // Ne traiter comme une erreur que si le streaming était encore actif
            if (isStreaming) {
                const streamingContentEl = document.getElementById('streaming-content');
                if (streamingContentEl) {
                     const currentText = streamingContentEl.innerText || '';
                     streamingContentEl.innerHTML = currentText + '\n\n<span class="text-danger">Erreur de connexion. Le streaming a été interrompu.</span>';
                }
                stopStreaming();
            }
        };
    }
    
    function stopStreaming() {
        if (streamSource) {
            streamSource.close();
            console.log('EventSource fermé.');
            streamSource = null;
        }
        isStreaming = false;
        
        const streamBtn = document.getElementById('stream-prompt-btn');
        const stopStreamBtn = document.getElementById('stop-stream-btn');
        const responseHeader = document.querySelector('#llm-response-area .card-header');

        if (streamBtn && stopStreamBtn) {
            streamBtn.disabled = false;
            streamBtn.classList.remove('d-none');
            stopStreamBtn.classList.add('d-none');
        }
        
        // Ne changer le titre que si c'est encore en cours de streaming (bg-info)
        // Si c'est déjà en succès (bg-success), ne pas le changer
        if (responseHeader && responseHeader.classList.contains('bg-info')) { // Si c'était en streaming et pas encore en succès
            responseHeader.innerHTML = 'Réponse du LLM (interrompue ou erreur)';
            responseHeader.classList.remove('bg-info');
            responseHeader.classList.add('bg-warning'); // Indiquer un état non complet
        }
            
        // Pas besoin d'ajouter le bouton copier ici, on le fait si data.done a été reçu.
        // Si on arrête manuellement, on ne sait pas si le contenu est complet.
    }
}); 