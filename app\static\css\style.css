/* Style principal pour PromptAchat */

/* Styles pour HTMX */
.htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in;
}
.htmx-request .htmx-indicator {
    opacity: 1;
}
.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Affichage Bootstrap supplémentaire */
.spinner-border {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    vertical-align: -0.125em;
    border: 0.2em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    -webkit-animation: .75s linear infinite spinner-border;
    animation: .75s linear infinite spinner-border;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* Styles spécifiques pour les prompts */
.prompt-preview-formatted {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

.llm-response {
    background-color: #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
}

/* Styles pour les cards et boutons */
.card {
    margin-bottom: 1rem;
}

.btn {
    position: relative;
}

.btn .htmx-indicator {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
} 