/**
 * Utilitaire de débogage pour HTMX
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log("Débogage HTMX activé - vérifiez la console pour les événements");
    console.log("URLs disponibles pour le débogage:");
    
    // Récupérer tous les éléments qui ont des attributs HTMX
    const htmxElements = document.querySelectorAll('[hx-get], [hx-post], [hx-put], [hx-delete]');
    htmxElements.forEach(element => {
        // Afficher chaque URL HTMX
        const getUrl = element.getAttribute('hx-get');
        const postUrl = element.getAttribute('hx-post');
        const putUrl = element.getAttribute('hx-put');
        const deleteUrl = element.getAttribute('hx-delete');
        const target = element.getAttribute('hx-target');
        const swap = element.getAttribute('hx-swap');
        const indicator = element.getAttribute('hx-indicator');
        
        console.log('Élément HTMX trouvé :', {
            element: element.outerHTML.substring(0, 100) + '...',
            get: getUrl,
            post: postUrl,
            put: putUrl,
            delete: deleteUrl,
            target: target,
            swap: swap,
            indicator: indicator
        });
    });
    
    // Logger les événements HTMX pour le débogage
    document.body.addEventListener('htmx:configRequest', function(event) {
        console.log('HTMX Configuration de la requête :', event.detail);
    });
    
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        console.log('HTMX Requête en cours vers:', event.detail.pathInfo ? event.detail.pathInfo.path : event.detail);
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        console.log('HTMX Requête terminée:', 
                    event.detail.pathInfo ? event.detail.pathInfo.path : event.detail,
                    'Status:', event.detail.xhr ? event.detail.xhr.status : 'Inconnu',
                    'Réponse:', event.detail.xhr ? event.detail.xhr.responseText.substring(0, 200) + '...' : 'Inconnue');
    });

    document.body.addEventListener('htmx:responseError', function(event) {
        console.error('HTMX Erreur de réponse:', 
                      event.detail.error, 
                      'Status:', event.detail.xhr ? event.detail.xhr.status : 'Inconnu',
                      'Réponse:', event.detail.xhr ? event.detail.xhr.responseText : 'Inconnue');
        
        // Éventuellement afficher un message d'erreur à l'utilisateur
        let targetElem = document.querySelector(event.detail.target);
        if (targetElem) {
            targetElem.innerHTML = `
            <div class="alert alert-danger">
              Une erreur est survenue lors de la communication avec le serveur. 
              <br>Statut: ${event.detail.xhr ? event.detail.xhr.status : 'Inconnu'}
              <br><pre>${event.detail.xhr ? event.detail.xhr.responseText.substring(0, 500) : 'Inconnue'}</pre>
              <br>Veuillez réessayer ou contacter l'administrateur.
            </div>`;
        }
    });

    document.body.addEventListener('htmx:sendError', function(event) {
        console.error('HTMX Erreur d\'envoi:', event.detail.error);
    });
}); 