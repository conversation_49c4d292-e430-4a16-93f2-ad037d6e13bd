"""
Service pour l'authentification LDAP.
"""
import logging
import ssl
from typing import Tuple, Optional, Dict, Any

from ldap3 import Connection, Server, Tls, ALL, SIMPLE
from ldap3.core.exceptions import LDAPException, LDAPBindError, LDAPSSLConfigurationError

from app.core.config import config_manager # Utiliser le config_manager global

logger = logging.getLogger(__name__)

class LdapConfigurationError(Exception):
    """Exception pour les erreurs de configuration LDAP."""
    pass

class LdapAuthenticationError(Exception):
    """Exception pour les échecs d'authentification LDAP."""
    pass

class LdapService:
    """Gère l'authentification auprès d'un serveur LDAP."""

    def __init__(self):
        self.ldap_enabled = config_manager.is_ldap_enabled()
        if not self.ldap_enabled:
            logger.warning("L'authentification LDAP est désactivée dans la configuration.")
            return

        self.server_url = config_manager.get_ldap_server()
        self.port = config_manager.get_ldap_port()
        self.user_dn_format = config_manager.get_ldap_user_dn_format()
        self.use_ssl = config_manager.use_ldap_ssl()
        self.tls_validate_cert = config_manager.validate_ldap_tls_cert()
        self.tls_ca_certs_file = config_manager.get_ldap_tls_ca_certs_file()
        self.tls_version_str = config_manager.get_ldap_tls_version()
        self.tls_ciphers = config_manager.get_ldap_tls_ciphers()

        if not self.server_url or not self.user_dn_format:
            msg = "Configuration LDAP incomplète : 'server' et 'user_dn_format' sont requis."
            logger.error(msg)
            raise LdapConfigurationError(msg)
        
        self.tls_config = None
        if self.use_ssl:
            self._setup_tls_config()

    def _setup_tls_config(self):
        """Configure l'objet Tls pour les connexions SSL/TLS."""
        validate = ssl.CERT_REQUIRED if self.tls_validate_cert else ssl.CERT_NONE
        tls_version_val = None
        if self.tls_version_str:
            try:
                tls_version_val = getattr(ssl, self.tls_version_str)
                logger.debug(f"Utilisation de la version TLS LDAP: {self.tls_version_str} ({tls_version_val})")
            except AttributeError:
                 logger.warning(f"Version TLS LDAP '{self.tls_version_str}' non reconnue. Utilisation de la négociation par défaut.")
        else:
             logger.debug("Aucune version TLS LDAP spécifique configurée, utilisation de la négociation par défaut.")

        try:
            self.tls_config = Tls(
                validate=validate,
                version=tls_version_val,
                ca_certs_file=self.tls_ca_certs_file if self.tls_ca_certs_file else None,
                ciphers=self.tls_ciphers if self.tls_ciphers else None
            )
            logger.info(f"Configuration TLS ldap3: validate={validate}, version={tls_version_val}, ca_certs_file={self.tls_ca_certs_file}, ciphers={self.tls_ciphers}")
        except Exception as e:
             msg = f"Erreur lors de la création de la configuration TLS ldap3: {e}"
             logger.error(msg, exc_info=True)
             raise LdapConfigurationError(msg)

    def authenticate(self, username: str, password: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Authentifie un utilisateur auprès du serveur LDAP.

        Args:
            username (str): Le nom d'utilisateur.
            password (str): Le mot de passe.

        Returns:
            Tuple[bool, Optional[Dict[str, Any]]]: (True, {"uid": username}) si succès.

        Raises:
            LdapConfigurationError: Si LDAP est désactivé ou mal configuré.
            LdapAuthenticationError: Si l'authentification échoue (ex: mauvais identifiants).
            LDAPException: Pour d'autres erreurs LDAP.
        """
        if not self.ldap_enabled:
            logger.warning("Tentative d'authentification LDAP alors que le service est désactivé.")
            # Ou retourner False, None ou lever une exception spécifique
            raise LdapConfigurationError("L'authentification LDAP est désactivée.")

        if not password: # Un mot de passe vide échouera probablement au bind, mais autant vérifier
            logger.warning(f"Tentative d'authentification LDAP pour '{username}' avec un mot de passe vide.")
            raise LdapAuthenticationError("Le mot de passe ne peut être vide.")

        try:
            user_dn = self.user_dn_format % username
        except TypeError: # Erreur si user_dn_format n'est pas une chaîne de formatage valide (ex: ne contient pas %s)
            msg = f"Erreur de formatage du DN utilisateur avec user_dn_format='{self.user_dn_format}' et username='{username}'"
            logger.error(msg)
            raise LdapConfigurationError(msg)

        logger.info(f"Tentative d'authentification LDAP pour DN: {user_dn} sur {self.server_url}:{self.port} (SSL: {self.use_ssl})")

        try:
            server = Server(self.server_url, port=self.port, get_info=ALL, use_ssl=self.use_ssl, tls=self.tls_config)
            
            # Connection avec auto_bind=True, raise_exceptions=True
            # Cela lèvera une exception (LDAPBindError) en cas d'échec du bind.
            with Connection(server, user=user_dn, password=password, authentication=SIMPLE, auto_bind=True, raise_exceptions=True) as conn:
                logger.info(f"Authentification LDAP réussie pour l'utilisateur '{username}' (DN: {user_dn})")
                # Ici, conn.extend.standard.who_am_i() pourrait retourner le DN réel si besoin, mais l'uid est username.
                user_attributes = {"uid": username} 
                return True, user_attributes

        except LDAPBindError as e:
            logger.warning(f"Échec de l'authentification LDAP pour l'utilisateur '{username}' (DN: {user_dn}): {e}")
            error_message = "Échec de l'authentification. Nom d'utilisateur ou mot de passe invalide."
            if hasattr(e, 'result') and e.result and 'description' in e.result:
                 if e.result['description'] == 'invalidCredentials':
                     logger.debug("LDAPBindError: Invalid Credentials.")
                 elif e.result['description'] == 'invalidDNSyntax':
                      logger.error(f"LDAPBindError: Syntaxe DN invalide pour '{user_dn}'. Vérifiez user_dn_format.")
                      error_message = f"Erreur de configuration LDAP (syntaxe DN invalide pour {user_dn})."
                 else:
                     logger.warning(f"LDAPBindError description: {e.result['description']}")
            raise LdapAuthenticationError(error_message) from e

        except LDAPSSLConfigurationError as e:
            logger.error(f"Erreur de configuration SSL/TLS LDAP: {e}", exc_info=True)
            raise LdapConfigurationError(f"Erreur de configuration SSL/TLS LDAP: {e}") from e
        
        except LDAPException as e: # Capture d'autres exceptions LDAP spécifiques
            logger.error(f"Erreur LDAP générique pour '{username}': {e}", exc_info=True)
            raise # Relance l'exception LDAP capturée

        except Exception as e: # Pour toute autre erreur inattendue
            logger.error(f"Erreur inattendue lors de l'authentification LDAP pour '{username}': {e}", exc_info=True)
            raise LdapAuthenticationError(f"Une erreur inattendue est survenue lors de la connexion LDAP: {e}") from e

# Instance unique pour un accès facile si nécessaire
# ldap_service = LdapService() # Sera typiquement injecté ou créé au besoin dans FastAPI 