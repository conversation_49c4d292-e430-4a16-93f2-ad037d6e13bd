/**
 * Initialisation et vérification de HTMX
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('Vérification de l\'initialisation de HTMX');
    
    // Vérifier si HTMX est chargé correctement
    if (typeof window.htmx === 'undefined') {
        console.error('HTMX n\'est pas chargé !');
        // Afficher un message d'erreur visible
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger fixed-top w-100 text-center';
        errorDiv.style.zIndex = '9999';
        errorDiv.innerHTML = 'ERREUR: La bibliothèque HTMX n\'est pas chargée correctement. Veuillez recharger la page ou contacter l\'administrateur.';
        document.body.prepend(errorDiv);
        
        // Tenter de recharger HTMX
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/htmx.org@1.9.6';
        script.onload = function() {
            console.log('HTMX a été rechargé avec succès!');
            errorDiv.className = 'alert alert-success fixed-top w-100 text-center';
            errorDiv.innerHTML = 'HTMX rechargé avec succès! Veuillez rafraîchir la page.';
            // Initialisation manuelle de HTMX
            if (typeof window.htmx !== 'undefined') {
                window.htmx.process(document.body);
            }
        };
        document.head.appendChild(script);
    } else {
        console.log('HTMX est correctement chargé. Version:', window.htmx.version);
        
        // Activer manuellement HTMX sur tous les éléments
        window.htmx.process(document.body);
        
        // Vérifier les attributs HTMX
        const htmxElements = document.querySelectorAll('[hx-get], [hx-post], [hx-put], [hx-delete]');
        console.log(`${htmxElements.length} éléments avec attributs HTMX trouvés`);
        
        // Fonction utilitaire pour détecter si un contenu HTML est une page de connexion
        function isLoginPage(html) {
            return html && 
                  (html.includes('Connexion') && html.includes('Mot de passe')) || 
                  html.includes('L\'authentification LDAP est actuellement désactivée');
        }
        
        // Fonction pour rediriger vers la page de connexion
        function redirectToLogin() {
            console.log('Page de connexion détectée dans une réponse HTMX, redirection...');
            
            // Récupérer et préserver le session_id pour la redirection
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');
            
            let loginUrl = '/auth/login';
            if (sessionId) {
                loginUrl += `?session_id=${sessionId}&return_to=${encodeURIComponent(window.location.pathname + window.location.search)}`;
            } else {
                loginUrl += `?return_to=${encodeURIComponent(window.location.pathname + window.location.search)}`;
            }
            
            window.location.href = loginUrl;
            return false;
        }
        
        // Intercepter les réponses HTMX pour détecter les pages de connexion
        document.body.addEventListener('htmx:beforeSwap', function(event) {
            console.log('HTMX beforeSwap - statut HTTP:', event.detail.xhr.status);
            
            // Vérifier si c'est une erreur d'authentification (401)
            if (event.detail.xhr.status === 401) {
                console.log('Erreur d\'authentification 401 détectée dans la réponse HTMX');
                event.preventDefault(); // Empêcher le swap
                redirectToLogin();
                return false;
            }
            
            // Vérifier si la réponse contient une page de connexion
            if (isLoginPage(event.detail.serverResponse)) {
                console.log('Page de connexion détectée dans la réponse HTMX');
                event.preventDefault(); // Empêcher le swap
                redirectToLogin();
                return false;
            }
        });
        
        // Intercepter les erreurs HTMX pour les gérer élégamment
        document.body.addEventListener('htmx:responseError', function(event) {
            console.log('HTMX responseError - statut HTTP:', event.detail.xhr.status);
            
            // Vérifier si c'est une erreur d'authentification (401)
            if (event.detail.xhr.status === 401) {
                console.log('Erreur d\'authentification 401 détectée dans une erreur HTMX');
                event.preventDefault(); // Empêcher le traitement par défaut
                redirectToLogin();
                return false;
            }
        });
        
        // Activer le logging HTMX
        window.htmx.logger = function(elt, event, data) {
            if(console) {
                console.log(event, elt, data);
            }
        };
        
        // Configurer HTMX pour inclure les cookies dans toutes les requêtes
        window.htmx.config.withCredentials = true; // Équivalent de credentials: 'include' pour fetch API
        
        window.htmx.config.historyCacheSize = 0;
        window.htmx.config.refreshOnHistoryMiss = true;
        window.htmx.config.defaultSwapStyle = 'innerHTML';
        window.htmx.config.includeIndicatorStyles = true;

        // Ajouter le session_id à toutes les requêtes HTMX
        document.body.addEventListener('htmx:configRequest', function(event) {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');
            
            if (sessionId) {
                // Ajouter le session_id au paramètre de requête
                if (event.detail.parameters) {
                    event.detail.parameters['session_id'] = sessionId;
                } else {
                    event.detail.parameters = { 'session_id': sessionId };
                }
                console.log('Session ID ajouté à la requête HTMX:', sessionId);
            }
        });
        
        // Fonction pour vérifier l'état d'authentification
        function checkAuthentication() {
            fetch('/auth/check-auth', {
                method: 'GET',
                credentials: 'include', // Inclure les cookies
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    if (response.status === 401) {
                        console.log('Session expirée détectée lors de la vérification d\'authentification');
                        redirectToLogin();
                    }
                    return Promise.reject('Erreur de vérification d\'authentification');
                }
                return response.json();
            })
            .then(data => {
                console.log('État d\'authentification vérifié:', data.authenticated);
            })
            .catch(error => {
                console.error('Erreur lors de la vérification d\'authentification:', error);
            });
        }
        
        // Vérifier l'authentification au chargement de la page
        checkAuthentication();
    }
}); 