{# templates/prompts/partials/cockpit_summary.html #}

{# Inclure les swaps OOB s'ils sont fournis. HTMX les traitera. #}
{% if oob_swaps %}
    {{ oob_swaps | safe }}
{% endif %}

<div class="card mt-2 border-info">
    <div class="card-header bg-info text-dark">
        Résumé des Données Cockpit (ID: {{ cockpit_id_retrieved }})
    </div>
    <div class="card-body" style="font-size: 0.9rem;">
        {% if summary_markdown %}
            {# Le markdown doit être converti en HTML par le serveur ou avec une lib JS côté client #}
            {# Pour l'instant, affichage brut ou via un filtre markdown si Jinja est configuré pour #}
            {# Supposons que nous n'avons pas de filtre markdown pour l'instant #}
            <pre style="white-space: pre-wrap; word-wrap: break-word;">{{ summary_markdown }}</pre>
            
            {# Alternative si vous avez une extension Markdown pour Jinja ou une lib JS #}
            {# <div class="markdown-content">{{ summary_markdown | markdown_to_html_filter | safe }}</div> #}
        {% else %}
            <p class="text-muted">Aucun résumé à afficher.</p>
        {% endif %}
    </div>
</div>

{# Optionnel: Afficher tous les champs bruts pour débogage ou inspection #}
{# 
<details class="mt-2">
    <summary class="text-muted small">Voir toutes les données Cockpit récupérées (brut)</summary>
    <pre style="font-size: 0.8rem; max-height: 200px; overflow-y: auto; background-color: #f8f9fa;">
        <code>{{ all_cockpit_fields | tojson(indent=2) }}</code>
    </pre>
</details>
#} 