{# templates/prompts/partials/prompt_list.html #}

{% if prompts %}
    <h3 class="mb-3">Prompts pour la catégorie : {{ category_name or category_slug }}</h3>
    <div class="list-group">
        {% for prompt in prompts %}
            <a href="{{ url_for('view_prompt_detail', prompt_id_for_url=prompt.id_for_url) }}"
               class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                
                <div>
                    <h5 class="mb-1">{{ prompt.title }}</h5>
                    {% if prompt.description %}
                        <small class="text-muted">{{ prompt.description | truncate(120) }}</small>
                    {% endif %}
                </div>
                
                {% if prompt.is_user_prompt %}
                    <span class="badge bg-secondary rounded-pill">Utilisateur</span>
                {% else %}
                    <span class="badge bg-success rounded-pill">Système</span>
                {% endif %}
            </a>
        {% endfor %}
    </div>
{% elif category_slug %}
    {# Si category_slug est défini mais pas de prompts, c'est qu'aucun prompt n'a été trouvé #}
    <div class="alert alert-info" role="alert">
        Aucun prompt trouvé pour la catégorie "{{ category_name or category_slug }}".
    </div>
{% else %}
    {# Ce cas ne devrait pas être atteint si la logique de l'endpoint est correcte #}
    <div class="alert alert-warning" role="alert">
        Veuillez sélectionner une catégorie.
    </div>
{% endif %}

<!-- Diagnostic info -->
<div class="mt-3">
    <details class="small text-muted">
        <summary>Informations techniques</summary>
        <pre class="small">
Route utilisée: list_prompts_for_category
Catégorie demandée: {{ category_slug }}
Nombre de prompts: {{ prompts|length if prompts else 0 }}
        </pre>
    </details>
</div> 