{% extends "base.html" %}

{% block title %}Accueil - PromptAchat{% endblock %}

{% block head %}
{{ super() }}
<script src="{{ url_for('static', path='/js/htmx-init.js') }}"></script>
<script src="{{ url_for('static', path='/js/htmx-debug.js') }}"></script>
<script>
// Script pour vérifier le fonctionnement des boutons
document.addEventListener('DOMContentLoaded', function() {
    // Test des boutons directement via JavaScript pour s'assurer qu'ils fonctionnent
    const testButton = document.getElementById('test-js-button');
    if (testButton) {
        testButton.addEventListener('click', function() {
            document.getElementById('js-test-result').textContent = 'Le JavaScript fonctionne correctement!';
        });
    }
    
    // Test manuel HTMX pour le bouton "Tester HTMX"
    const htmxTestBtn = document.getElementById('htmx-test-btn');
    if (htmxTestBtn) {
        htmxTestBtn.addEventListener('click', function() {
            // Ignorer HTMX et faire la requête manuellement
            fetch('/')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('htmx-test-result').innerHTML = 
                        '<div class="alert alert-success">Requête fetch réussie!</div>';
                })
                .catch(error => {
                    document.getElementById('htmx-test-result').innerHTML = 
                        '<div class="alert alert-danger">Erreur: ' + error.message + '</div>';
                });
        });
    }

    // Fonction pour vérifier l'état de l'authentification
    function checkAuthStatus() {
        fetch('/auth/check-auth', {
            credentials: 'include',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok && response.status === 401) {
                console.log('Session expirée détectée lors de la vérification');
                // Rediriger vers la page de connexion
                window.location.href = '/auth/login';
            }
        })
        .catch(error => {
            console.error('Erreur lors de la vérification de l\'authentification:', error);
        });
    }

    // Vérifier l'authentification toutes les 5 minutes
    // setInterval(checkAuthStatus, 5 * 60 * 1000);
});
</script>
{% endblock %}

{% block content %}
{# Header de la page - Présent sur la plupart des pages une fois connecté #}
<header class="py-3 mb-4 border-bottom bg-light">
    <div class="container d-flex flex-wrap justify-content-between align-items-center">
        <a href="{{ url_for('root') }}" class="d-flex align-items-center mb-2 mb-lg-0 text-dark text-decoration-none">
            {# Remplacer par un vrai logo si disponible #}
            <span class="fs-4">🚀 PromptAchat</span> 
        </a>

        <div class="text-end">
            {% if user %}
                <span class="me-3">Bonjour, {{ user.uid }}</span>
                <form hx-post="{{ url_for('logout') }}" hx-target="body" class="d-inline">
                    <button type="submit" class="btn btn-outline-danger btn-sm">Déconnexion</button>
                </form>
            {% else %}
                {# Normalement, l'utilisateur devrait toujours être défini ici à cause du middleware/Depends #}
                <a href="{{ url_for('login_page') }}" class="btn btn-outline-primary btn-sm">Connexion</a>
            {% endif %}
        </div>
    </div>
</header>

<div class="container">
    <h1 class="mb-4">Sélectionnez une catégorie de prompts</h1>

    <!-- Test JavaScript -->
    <div class="alert alert-primary mb-4">
        <h5>Test JavaScript (sans HTMX)</h5>
        <button id="test-js-button" class="btn btn-primary">Tester JavaScript</button>
        <div id="js-test-result" class="mt-2 p-2 border"></div>
    </div>
    
    <!-- Test HTMX -->
    <div class="alert alert-warning mb-4">
        <h5>Test HTMX</h5>
        <button 
            id="htmx-test-btn"
            class="btn btn-secondary"
            hx-get="{{ url_for('test_htmx') }}"
            hx-target="#htmx-test-result"
            hx-swap="innerHTML"
            hx-trigger="click"
        >
            Tester HTMX
        </button>
        <div id="htmx-test-result" class="mt-2 p-2 border">
            Le résultat du test HTMX s'affichera ici
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6 mb-2 mb-md-0">
            <a 
                href="/prompts/list/internal{% if session_id %}?session_id={{ session_id }}{% endif %}" 
                class="btn btn-primary btn-lg w-100 p-3"
                onclick="window.navigateToInternal(event)"
                data-session-id="{{ session_id if session_id else '' }}"
            >
                Données Internes (JavaScript)
                <span id="loading-indicator-internal" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
            </a>
        </div>
        <div class="col-md-6">
            <a 
                href="/prompts/list/external{% if session_id %}?session_id={{ session_id }}{% endif %}"
                class="btn btn-info btn-lg w-100 p-3 text-dark"
                onclick="window.navigateToExternal(event)"
                data-session-id="{{ session_id if session_id else '' }}"
            >
                Données Externes (JavaScript)
                <span id="loading-indicator-external" class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
            </a>
        </div>
    </div>

    <script>
        // Fonctions JavaScript pour remplacer HTMX
        window.navigateToInternal = function(event) {
            event.preventDefault();
            document.getElementById('loading-indicator-internal').classList.remove('d-none');
            
            // Récupérer la session_id depuis l'élément ou les cookies
            let sessionId = event.currentTarget.getAttribute('data-session-id');
            let url = '/prompts/list/internal';
            if (sessionId) {
                url += '?session_id=' + sessionId;
            }
            
            // Ajouter les credentials pour envoyer les cookies
            fetch(url, {
                credentials: 'include', // Ceci est important pour envoyer les cookies de session
                headers: {
                    'Accept': 'text/html',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('Réponse status:', response.status);
                    console.log('Headers:', [...response.headers.entries()]);
                    
                    if (!response.ok) {
                        if (response.status === 302 || response.status === 303) {
                            // Redirection - Récupérer l'URL de redirection et y aller
                            const redirectUrl = response.headers.get('Location');
                            console.log('Redirection détectée vers:', redirectUrl);
                            if (redirectUrl) {
                                window.location.href = redirectUrl;
                                return Promise.reject('Redirection en cours...');
                            }
                            throw new Error('Redirection détectée. Vous devez vous reconnecter.');
                        }
                        else if (response.status === 401) {
                            // Session expirée ou non authentifiée
                            const loginRequired = response.headers.get('X-Login-Required');
                            const loginUrl = response.headers.get('X-Login-URL');
                            
                            console.log('Session expirée ou non authentifiée détectée');
                            console.log('Login requis:', loginRequired);
                            console.log('URL de login:', loginUrl);
                            
                            if (loginUrl) {
                                // Rediriger vers la page de login
                                window.location.href = loginUrl;
                                return Promise.reject('Redirection vers la page de login...');
                            }
                            
                            // Si on n'a pas d'URL de redirection, afficher un message général
                            throw new Error('Session expirée. Veuillez vous reconnecter.');
                        }
                        throw new Error('Erreur HTTP: ' + response.status);
                    }
                    
                    // Vérifier le type de contenu de la réponse
                    const contentType = response.headers.get('Content-Type') || '';
                    if (contentType.includes('application/json')) {
                        return response.json().then(data => {
                            if (data.error) {
                                throw new Error(data.error);
                            }
                            return data;
                        });
                    }
                    
                    return response.text();
                })
                .then(data => {
                    // Vérifier si la donnée est un objet JSON ou une chaîne HTML
                    if (typeof data === 'object') {
                        // Réponse JSON
                        document.getElementById('prompt-list-area').innerHTML = 
                            `<div class="alert alert-info">Réponse du serveur : ${JSON.stringify(data)}</div>`;
                    } else {
                        // Réponse HTML
                        // Vérifier si le HTML contient une page de connexion
                        if (data.includes('Connexion') && data.includes('Mot de passe')) {
                            console.log('Page de connexion détectée dans la réponse HTML');
                            // Rediriger vers la page de connexion
                            window.location.href = '/auth/login';
                            return;
                        }
                        document.getElementById('prompt-list-area').innerHTML = data;
                    }
                })
                .catch(error => {
                    console.error('Erreur fetch:', error);
                    document.getElementById('prompt-list-area').innerHTML = 
                        '<div class="alert alert-danger">Erreur lors du chargement des prompts internes: ' + error.message + '</div>';
                })
                .finally(() => {
                    document.getElementById('loading-indicator-internal').classList.add('d-none');
                });
        };
        
        window.navigateToExternal = function(event) {
            event.preventDefault();
            document.getElementById('loading-indicator-external').classList.remove('d-none');
            
            // Récupérer la session_id depuis l'élément ou les cookies
            let sessionId = event.currentTarget.getAttribute('data-session-id');
            let url = '/prompts/list/external';
            if (sessionId) {
                url += '?session_id=' + sessionId;
            }
            
            // Ajouter les credentials pour envoyer les cookies
            fetch(url, {
                credentials: 'include', // Ceci est important pour envoyer les cookies de session
                headers: {
                    'Accept': 'text/html',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('Réponse status:', response.status);
                    console.log('Headers:', [...response.headers.entries()]);
                    
                    if (!response.ok) {
                        if (response.status === 302 || response.status === 303) {
                            // Redirection - Récupérer l'URL de redirection et y aller
                            const redirectUrl = response.headers.get('Location');
                            console.log('Redirection détectée vers:', redirectUrl);
                            if (redirectUrl) {
                                window.location.href = redirectUrl;
                                return Promise.reject('Redirection en cours...');
                            }
                            throw new Error('Redirection détectée. Vous devez vous reconnecter.');
                        }
                        else if (response.status === 401) {
                            // Session expirée ou non authentifiée
                            const loginRequired = response.headers.get('X-Login-Required');
                            const loginUrl = response.headers.get('X-Login-URL');
                            
                            console.log('Session expirée ou non authentifiée détectée');
                            console.log('Login requis:', loginRequired);
                            console.log('URL de login:', loginUrl);
                            
                            if (loginUrl) {
                                // Rediriger vers la page de login
                                window.location.href = loginUrl;
                                return Promise.reject('Redirection vers la page de login...');
                            }
                            
                            // Si on n'a pas d'URL de redirection, afficher un message général
                            throw new Error('Session expirée. Veuillez vous reconnecter.');
                        }
                        throw new Error('Erreur HTTP: ' + response.status);
                    }
                    
                    // Vérifier le type de contenu de la réponse
                    const contentType = response.headers.get('Content-Type') || '';
                    if (contentType.includes('application/json')) {
                        return response.json().then(data => {
                            if (data.error) {
                                throw new Error(data.error);
                            }
                            return data;
                        });
                    }
                    
                    return response.text();
                })
                .then(data => {
                    // Vérifier si la donnée est un objet JSON ou une chaîne HTML
                    if (typeof data === 'object') {
                        // Réponse JSON
                        document.getElementById('prompt-list-area').innerHTML = 
                            `<div class="alert alert-info">Réponse du serveur : ${JSON.stringify(data)}</div>`;
                    } else {
                        // Réponse HTML
                        // Vérifier si le HTML contient une page de connexion
                        if (data.includes('Connexion') && data.includes('Mot de passe')) {
                            console.log('Page de connexion détectée dans la réponse HTML');
                            // Rediriger vers la page de connexion
                            window.location.href = '/auth/login';
                            return;
                        }
                        document.getElementById('prompt-list-area').innerHTML = data;
                    }
                })
                .catch(error => {
                    console.error('Erreur fetch:', error);
                    document.getElementById('prompt-list-area').innerHTML = 
                        '<div class="alert alert-danger">Erreur lors du chargement des prompts externes: ' + error.message + '</div>';
                })
                .finally(() => {
                    document.getElementById('loading-indicator-external').classList.add('d-none');
                });
        };

        // Attendre que le DOM soit chargé pour attacher les événements aux boutons de test API
        document.addEventListener('DOMContentLoaded', function() {
            // Sélectionner tous les liens de test API
            const apiTestButtons = document.querySelectorAll('.btn-outline-primary, .btn-outline-info');
            
            // Attacher un gestionnaire à chaque bouton
            apiTestButtons.forEach(button => {
                button.addEventListener('click', function(event) {
                    // Empêcher la navigation par défaut
                    event.preventDefault();
                    
                    const url = this.getAttribute('href');
                    console.log('Test API direct vers:', url);
                    
                    // Ouvrir une nouvelle fenêtre avec l'URL
                    window.open(url, '_blank');
                });
            });
        });
    </script>

    {# Zone où la liste des prompts sera chargée par HTMX #}
    <div id="prompt-list-area" class="mt-4">
        {# Peut contenir un message initial si souhaité #}
        <p class="text-muted text-center">Veuillez sélectionner une catégorie pour afficher les prompts.</p>
    </div>

    <!-- Indicateur de chargement global -->
    <div class="position-fixed top-0 start-0 end-0" style="z-index: 1050;">
        <div class="alert alert-info htmx-indicator" id="global-indicator" role="alert">
            Chargement en cours...
        </div>
    </div>

    <!-- Zone de débogage (à supprimer en production) -->
    <div class="mt-5 border-top pt-3">
        <details open>
            <summary class="text-muted small">Informations de débogage</summary>
            <div class="card mt-2">
                <div class="card-body">
                    <h5 class="card-title">Chemins d'API</h5>
                    <ul class="small">
                        <li>Liste interne: <code>/prompts/list/internal{% if session_id %}?session_id={{ session_id }}{% endif %}</code></li>
                        <li>Liste externe: <code>/prompts/list/external{% if session_id %}?session_id={{ session_id }}{% endif %}</code></li>
                    </ul>
                    
                    <h5 class="card-title mt-3">Test manuel des chemins d'API</h5>
                    <div class="d-flex gap-2">
                        <a href="/prompts/list/internal{% if session_id %}?session_id={{ session_id }}{% endif %}" class="btn btn-sm btn-outline-primary" target="_blank">Tester API Internal</a>
                        <a href="/prompts/list/external{% if session_id %}?session_id={{ session_id }}{% endif %}" class="btn btn-sm btn-outline-info" target="_blank">Tester API External</a>
                    </div>

                    <h5 class="card-title mt-3">Test htmx.js</h5>
                    <div id="htmx-version"></div>
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            const version = window.htmx ? window.htmx.version : "Non chargé";
                            document.getElementById('htmx-version').textContent = 'Version HTMX : ' + version;
                        });
                    </script>
                    
                    <h5 class="card-title mt-3">Informations d'authentification</h5>
                    <ul class="small">
                        <li>Session ID: <code>{{ session_id if session_id else 'Non disponible' }}</code></li>
                        <li>Utilisateur: <code>{{ user.uid if user else 'Non connecté' }}</code></li>
                    </ul>
                </div>
            </div>
        </details>
    </div>
</div>

{% endblock %} 