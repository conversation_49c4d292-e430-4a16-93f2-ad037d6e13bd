{% macro prompt_preview(prompt_content, variables_values={}, files_text=[], cockpit_data={}) %}
{# Macro pour prévisualiser un prompt avec les variables remplacées #}
{% if prompt_content %}
    {% set preview_content = prompt_content %}
    {% for var_name, var_value in variables_values.items() %}
        {% set var_placeholder = '{' + var_name + '}' %}
        {% set preview_content = preview_content | replace(var_placeholder, var_value) %}
    {% endfor %}
    <div class="prompt-preview-formatted">
        <pre style="white-space: pre-wrap; word-wrap: break-word;">{{ preview_content }}</pre>
    </div>
{% else %}
    <em class="text-muted">Contenu du prompt non disponible.</em>
{% endif %}
{% endmacro %}

{% macro loading_spinner(id="loading-indicator", size="sm", text="Chargement...") %}
{# Macro pour afficher un spinner de chargement #}
<div id="{{ id }}" class="d-none htmx-indicator">
    <div class="d-flex align-items-center">
        <div class="spinner-border spinner-border-{{ size }} me-2" role="status" aria-hidden="true"></div>
        <span>{{ text }}</span>
    </div>
</div>
{% endmacro %}

{% macro alert(message, type="info", dismissible=true) %}
{# Macro pour afficher une alerte #}
<div class="alert alert-{{ type }} {% if dismissible %}alert-dismissible fade show{% endif %}" role="alert">
    {{ message }}
    {% if dismissible %}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
    {% endif %}
</div>
{% endmacro %} 