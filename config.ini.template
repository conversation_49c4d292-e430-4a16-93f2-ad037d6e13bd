[internal]
url = https://api.openai.com/v1
api_key = votre_cle_api_ici
default_model = gpt-4.1

[oneapi]
# Mettre true pour activer OneAPI
use_oneapi = false                             
oneapi_url = https://endpoint.iag.oneapi.fr/
oneapi_bearer_token = votre_cle_api_ici

[external]
url = https://api.openai.com/v1
api_key = votre_cle_api_ici
default_model = gpt-3.5-turbo

[ollama]
enabled = false
url = http://192.168.23.21:11434/v1
default_model = mixtral:7b

[Cockpit]
api_url = https://cockpitdag.aas.edf.fr/api/v1/hlk/issues
api_key = votre_cle_api_ici
verify_cert_path = certs/cockpitdag_aas_edf_fr-bundle.pem

[Logging]
# Niveau de log par défaut (DEBUG, INFO, WARNING, ERROR, CRITICAL)
default_level = DEBUG

# Chemin du fichier de log (optionnel)
# log_file = logs/processor.log

[LDAP]
# Activer ou désactiver l'authentification LDAP
enabled = false
# Adresse du serveur LDAP
server = noe-gardiansesame.edf.fr
# Port du serveur LDAP (636 pour LDAPS par défaut)
port = 636
# DN de base pour la recherche (peut ne pas être nécessaire avec user_dn_format)
base = dc=gardiansesame
# Attribut utilisé comme identifiant utilisateur (ex: uid, sAMAccountName)
# user_id_attribute = "uid"
# Format du DN utilisateur pour le bind direct. %s sera remplacé par le username.
# --- MODIFICATION ICI : Guillemets retirés ---
user_dn_format = uid=%s,ou=People,dc=gardiansesame
# --- FIN MODIFICATION ---
# Utiliser SSL/TLS (true/false) - fortement recommandé
use_ssl = true
# Version TLS à utiliser (ex: PROTOCOL_TLSv1_1, PROTOCOL_TLSv1_2, PROTOCOL_TLS_CLIENT).
# NOTE: Ce serveur spécifique semble nécessiter TLSv1_1.
tls_version = PROTOCOL_TLSv1_1
# Valider le certificat du serveur (true/false) - fortement recommandé si use_ssl est true
tls_validate_cert = true
# Chemin vers un fichier CA bundle personnalisé (optionnel, si tls_validate_cert est true et le CA n'est pas dans le trust store système)
# tls_ca_certs_file = /path/to/ca/bundle.pem
# Configuration des ciphers TLS (laisser vide pour les défauts système)
# NOTE: Ce serveur spécifique semble nécessiter SECLEVEL=0.
tls_ciphers = DEFAULT:@SECLEVEL=0

[Database]
# Chemin vers le fichier de base de données SQLite pour les autorisations
user_auth_db_path = user_auth.db

[App]
# Nom de l'instance pour adapter le logo (edf ou enedis)
name = edf
# Email de contact affiché sur la page de login pour les demandes d'accès
access_contact_email = <EMAIL>
# UID utilisé en mode développeur quand LDAP est désactivé
developer_uid = dev_user

[Security]
# Liste des UIDs des administrateurs initiaux/permanents, séparés par des virgules
# Ces admins ne peuvent pas être supprimés ou rétrogradés via l'interface.
initial_admin_uids = e85949,e60680

[API_KEYS]
# Les clés DEFAULT_INTERNAL_MODEL, DEFAULT_EXTERNAL_MODEL, DEFAULT_OLLAMA_MODEL, CONFIDENTIALITY_CHECK_MODEL ont été déplacées ou supprimées.

[LLM]
default_temperature = 0.7
