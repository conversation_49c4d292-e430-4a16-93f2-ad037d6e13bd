import configparser
import logging
import os
from typing import Dict, Any, Optional, List
from pathlib import Path

# Chemin vers la racine du projet FastAPI (promptachat_fastapi)
# __file__ est app/core/config.py -> .parent est app/core -> .parent est app/ -> .parent est promptachat_fastapi/
PROJECT_ROOT_DIR = Path(__file__).resolve().parent.parent.parent
CONFIG_FILE_PATH = PROJECT_ROOT_DIR / "config.ini"

logger = logging.getLogger(__name__)

DEFAULT_LOG_FILE_PATH = "logs/app.log"
DEFAULT_PROMPTS_FILE = "prompts.json"
DEFAULT_USER_PROMPTS_FILE = "user_prompts.json"
DEFAULT_USER_DB_PATH = "user_auth.db"
DEFAULT_DEVELOPER_UID = "dev_user" # Ajout d'une valeur par défaut

class ConfigManager:
    """
    Classe pour gérer la configuration de l'application.
    Charge les paramètres depuis le fichier config.ini.
    """

    def __init__(self, config_file: Path = CONFIG_FILE_PATH):
        """
        Initialise le gestionnaire de configuration.

        Args:
            config_file (Path): Chemin vers le fichier de configuration.
        """
        self.config_file = config_file
        self.config = configparser.ConfigParser(interpolation=None)
        self.load_config()

    def load_config(self) -> None:
        """
        Charge la configuration depuis le fichier config.ini.
        Si le fichier n'existe pas, un avertissement est enregistré.
        """
        if self.config_file.exists():
            try:
                self.config.read(self.config_file, encoding='utf-8')
                logger.info(f"Configuration chargée depuis {self.config_file}")
            except Exception as e:
                logger.error(f"Erreur lors du chargement de la configuration ({self.config_file}): {e}", exc_info=True)
        else:
            logger.warning(f"Fichier de configuration {self.config_file} non trouvé. Utilisation des valeurs par défaut si définies, sinon None.")

    def get(self, section: str, key: str, fallback: Any = None) -> Any:
        """
        Récupère une valeur de configuration.
        """
        try:
            if section in self.config and key in self.config[section]:
                return self.config[section].get(key, fallback)
            # logger.debug(f"Clé [{section}].{key} non trouvée, utilisation du fallback: {fallback}")
            return fallback
        except Exception as e:
            logger.error(f"Erreur lors de la lecture de la config [{section}].{key}: {e}")
            return fallback

    def getboolean(self, section: str, key: str, fallback: bool = False) -> bool:
        """
        Récupère une valeur booléenne de configuration.
        """
        try:
            # Utiliser self.config.getboolean qui gère 'yes', 'on', 'true', '1', etc.
            # et qui lève ValueError si la section/clé n'existe pas ou si la valeur n'est pas un booléen valide.
            if section in self.config and key in self.config[section]:
                 return self.config.getboolean(section, key)
            # logger.debug(f"Clé booléenne [{section}].{key} non trouvée, utilisation du fallback: {fallback}")
            return fallback
        except ValueError:
             current_value = self.config.get(section,key, fallback=str(fallback)) # Pour le log
             logger.warning(f"Valeur non booléenne pour [{section}].{key}: '{current_value}'. Utilisation du fallback: {fallback}.")
             return fallback
        except Exception as e: # Autres exceptions possibles (ex: configparser.NoSectionError)
            logger.error(f"Erreur lors de la lecture booléenne de la config [{section}].{key}: {e}")
            return fallback

    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """
        Récupère une valeur entière de configuration.
        """
        try:
            if section in self.config and key in self.config[section]:
                return self.config.getint(section, key)
            # logger.debug(f"Clé entière [{section}].{key} non trouvée, utilisation du fallback: {fallback}")
            return fallback
        except ValueError:
            current_value = self.config.get(section,key, fallback=str(fallback)) # Pour le log
            logger.warning(f"Valeur non entière pour [{section}].{key}: '{current_value}'. Utilisation du fallback: {fallback}.")
            return fallback
        except Exception as e:
            logger.error(f"Erreur lors de la lecture entière de la config [{section}].{key}: {e}")
            return fallback

    def getfloat(self, section: str, key: str, fallback: float = 0.0) -> float:
        """
        Récupère une valeur flottante de configuration.
        """
        try:
            if section in self.config and key in self.config[section]:
                return self.config.getfloat(section, key)
            # logger.debug(f"Clé flottante [{section}].{key} non trouvée, utilisation du fallback: {fallback}")
            return fallback
        except ValueError:
            current_value = self.config.get(section,key, fallback=str(fallback)) # Pour le log
            logger.warning(f"Valeur non flottante pour [{section}].{key}: '{current_value}'. Utilisation du fallback: {fallback}.")
            return fallback
        except Exception as e:
            logger.error(f"Erreur lors de la lecture flottante de la config [{section}].{key}: {e}")
            return fallback

    def getlist(self, section: str, key: str, fallback: Optional[List[str]] = None, delimiter: str = ',') -> List[str]:
        """
        Récupère une liste de chaînes depuis une valeur de configuration séparée par un délimiteur.
        """
        _fallback = [] if fallback is None else fallback
        value_str = self.get(section, key, fallback=None) # Obtenir la chaîne brute ou None

        if value_str is None or not value_str.strip():
            # logger.debug(f"Clé liste [{section}].{key} non trouvée ou vide, utilisation du fallback: {_fallback}")
            return _fallback
        try:
            items = [item.strip() for item in value_str.split(delimiter) if item.strip()]
            return items
        except Exception as e:
            logger.error(f"Erreur lors de la lecture de la liste depuis la config [{section}].{key}: {e}")
            return _fallback

    def get_section(self, section: str) -> Dict[str, str]:
        """
        Récupère une section entière de configuration.
        """
        if section in self.config:
            return dict(self.config[section])
        return {}

    def mask_api_key(self, key_value: Optional[str]) -> str:
        """
        Masque une clé API pour l'affichage dans les logs.
        """
        if not key_value:
            return ""
        if len(key_value) > 7: # e.g. sk-xxxx...xxxx
            return f"{key_value[:4]}...{key_value[-4:]}"
        elif len(key_value) > 4:
             return f"{key_value[:2]}...{key_value[-2:]}"
        return "****" # Pour les clés très courtes ou non masquables de manière significative

    # Getters spécifiques (à adapter/compléter selon les besoins de FastAPI)
    # Exemple:
    def get_app_name(self) -> str:
        return self.config.get('app', 'name', fallback='PromptAchat')

    def get_default_log_level(self) -> str:
        return self.get("Logging", "default_level", fallback="INFO").upper()
    
    def get_log_file(self) -> str:
        return self.config.get('logging', 'log_file', fallback=DEFAULT_LOG_FILE_PATH)

    def is_ldap_enabled(self) -> bool:
        return self.getboolean("LDAP", "enabled", fallback=False)

    def get_ldap_server(self) -> Optional[str]:
        return self.get("LDAP", "server")

    def get_ldap_port(self) -> int:
        return self.getint("LDAP", "port", fallback=636)

    def get_ldap_user_dn_format(self) -> Optional[str]:
        return self.get("LDAP", "user_dn_format")
        
    def get_ldap_base_dn(self) -> Optional[str]: # Ajouté pour être complet
        return self.get("LDAP", "base")

    def use_ldap_ssl(self) -> bool:
        return self.getboolean("LDAP", "use_ssl", fallback=True)

    def get_ldap_tls_version(self) -> Optional[str]: # Le fichier config spécifie PROTOCOL_TLSv1_1
        return self.get("LDAP", "tls_version")

    def validate_ldap_tls_cert(self) -> bool:
        return self.getboolean("LDAP", "tls_validate_cert", fallback=True)
        
    def get_ldap_tls_ca_certs_file(self) -> Optional[str]:
        ca_path = self.get("LDAP", "tls_ca_certs_file")
        if ca_path and not os.path.isabs(ca_path):
            return str(PROJECT_ROOT_DIR / ca_path)
        return ca_path

    def get_ldap_tls_ciphers(self) -> Optional[str]:
        return self.get("LDAP", "tls_ciphers")

    def get_user_auth_db_path(self) -> Optional[str]:
        db_path_str = self.get("Database", "user_auth_db_path", fallback=None)
        if db_path_str:
            # Assurer que le chemin est absolu, relatif à la racine du projet si non absolu
            if not os.path.isabs(db_path_str):
                return str(PROJECT_ROOT_DIR / db_path_str)
            return db_path_str
        return None
        
    def get_initial_admin_uids(self) -> List[str]:
        return self.getlist("Security", "initial_admin_uids", fallback=[])

    def get_session_cookie_max_age_seconds(self) -> int:
        return self.getint("Security", "session_cookie_max_age_seconds", fallback=86400 * 7) # 7 jours par défaut

    def get_session_cookie_secure(self) -> bool:
        return self.getboolean("Security", "session_cookie_secure", fallback=False) # False par défaut (pour HTTP en dev)

    def get_session_cookie_samesite(self) -> str:
        return self.get("Security", "session_cookie_samesite", fallback="lax")

    def get_access_contact_email(self) -> str:
        return self.get("App", "access_contact_email", fallback="<EMAIL>")

    # --- Getters pour LLMClient (gardés de l'original, à vérifier pour pertinence) ---
    def get_internal_api_url(self) -> Optional[str]:
        return self.get("internal", "url")

    def get_internal_api_key(self) -> Optional[str]:
        return self.get("internal", "api_key")

    def is_one_api_enabled(self) -> bool:
        return self.getboolean("oneapi", "use_oneapi", fallback=False)

    def get_one_api_url(self) -> Optional[str]:
        return self.get("oneapi", "oneapi_url")

    def get_one_api_token(self) -> Optional[str]:
        return self.get("oneapi", "oneapi_bearer_token")

    def get_external_api_url(self) -> Optional[str]:
        return self.get("external", "url")

    def get_external_api_key(self) -> Optional[str]:
        return self.get("external", "api_key")

    def is_ollama_enabled(self) -> bool:
        return self.getboolean("ollama", "enabled", fallback=False)

    def get_ollama_api_url(self) -> Optional[str]:
        return self.get("ollama", "url")
    
    def get_ollama_model(self) -> str: # Differs from get_default_ollama_model
        return self.get("ollama", "model", fallback="mixtral:8x7b") # Utilise la clé "model" direct

    def get_default_internal_model(self) -> str:
        return self.get("API_KEYS", "DEFAULT_INTERNAL_MODEL", fallback="gpt-4.1")

    def get_default_external_model(self) -> str:
        return self.get("API_KEYS", "DEFAULT_EXTERNAL_MODEL", fallback="gpt-3.5-turbo")

    def get_default_ollama_model(self) -> str: # Utilise la section API_KEYS
        return self.get("API_KEYS", "DEFAULT_OLLAMA_MODEL", fallback=self.get_ollama_model()) # Fallback sur le modèle spécifique ollama

    def get_confidentiality_check_model(self) -> str:
        return self.get("API_KEYS", "CONFIDENTIALITY_CHECK_MODEL", fallback=self.get_default_internal_model())

    def get_default_temperature(self) -> float:
        return self.getfloat("LLM", "default_temperature", fallback=0.7)
        
    def get_cockpit_api_url(self) -> Optional[str]:
        return self.get("Cockpit", "api_url")

    def get_cockpit_api_key(self) -> Optional[str]:
        return self.get("Cockpit", "api_key")

    def get_cockpit_verify_cert_path(self) -> Optional[str]:
        cert_path = self.get("Cockpit", "verify_cert_path")
        if cert_path and not os.path.isabs(cert_path):
            # Le chemin dans config.ini est 'certs/cockpitdag_aas_edf_fr-bundle.pem'
            # Il faut le rendre absolu par rapport à la racine du projet
            return str(PROJECT_ROOT_DIR / cert_path)
        return cert_path

    def get_default_prompts_file(self) -> Path:
        return self.project_root / self.config.get('paths', 'prompts_file', fallback=DEFAULT_PROMPTS_FILE)

    def get_default_user_prompts_file(self) -> Path:
        return self.project_root / self.config.get('paths', 'user_prompts_file', fallback=DEFAULT_USER_PROMPTS_FILE)

    def get_user_db_path(self) -> Path:
        return self.project_root / self.config.get('paths', 'user_db_path', fallback=DEFAULT_USER_DB_PATH)

    def get_developer_uid(self) -> str:
        return self.config.get('app', 'developer_uid', fallback=DEFAULT_DEVELOPER_UID)

# Instance unique du gestionnaire de configuration
# S'assure que l'initialisation est faite une seule fois.
# Cette instance sera importée dans les autres modules.
config_manager = ConfigManager()

# Pour tester directement ce module (optionnel)
if __name__ == '__main__':
    print(f"Chemin du fichier de configuration: {CONFIG_FILE_PATH}")
    print(f"Le fichier config existe: {CONFIG_FILE_PATH.exists()}")
    
    print(f"Nom de l'application: {config_manager.get_app_name()}")
    print(f"Niveau de log par défaut: {config_manager.get_default_log_level()}")
    print(f"Fichier de log: {config_manager.get_log_file()}")
    print(f"LDAP activé: {config_manager.is_ldap_enabled()}")
    print(f"Serveur LDAP: {config_manager.get_ldap_server()}")
    print(f"Chemin DB Auth: {config_manager.get_user_auth_db_path()}")
    print(f"Admins initiaux: {config_manager.get_initial_admin_uids()}")
    print(f"Internal API URL: {config_manager.get_internal_api_url()}")
    print(f"Internal API Key (masquée): {config_manager.mask_api_key(config_manager.get_internal_api_key())}")
    print(f"Modèle interne par défaut: {config_manager.get_default_internal_model()}")
    print(f"Modèle Ollama (spécifique): {config_manager.get_ollama_model()}")
    print(f"Modèle Ollama (défaut API_KEYS): {config_manager.get_default_ollama_model()}")
    print(f"Chemin certif Cockpit: {config_manager.get_cockpit_verify_cert_path()}")
    # Test d'une clé qui n'existe pas avec fallback
    print(f"Clé inexistante: {config_manager.get('FakeSection', 'FakeKey', fallback='valeur_par_defaut')}")
    # Test getlist
    print(f"Liste des admins: {config_manager.getlist('Security', 'initial_admin_uids')}")
    print(f"Liste vide: {config_manager.getlist('FakeSection', 'FakeListKey', fallback=[])}")
    print(f"Liste avec fallback non vide: {config_manager.getlist('FakeSection', 'FakeListKeyWithFallback', fallback=['a', 'b'])}") 