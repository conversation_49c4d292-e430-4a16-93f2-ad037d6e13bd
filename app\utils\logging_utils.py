"""
Utilitaires de journalisation (logging) pour l'application FastAPI.
"""

import logging
import os
from typing import Optional
from pathlib import Path

# Import de l'instance de configuration pour accéder aux paramètres de logging
# Предполагается, что config_manager находится в app.core.config
# Для избежания циклических импортов, если logging_utils используется ConfigManager,
# лучше передавать параметры конфигурации напрямую в setup_logging.
# Однако, для простоты, если ConfigManager не использует logging_utils для своей инициализации,
# прямой импорт возможен.
# from app.core.config import config_manager # Décommenter si config_manager n'utilise pas ce module

# Chemin vers la racine du projet FastAPI (promptachat_fastapi)
# __file__ est app/utils/logging_utils.py -> .parent est app/utils -> .parent est app/ -> .parent est promptachat_fastapi/
PROJECT_ROOT_DIR = Path(__file__).resolve().parent.parent.parent

def setup_logging(log_level_str: str = "INFO", log_file_path_str: Optional[str] = None) -> None:
    """
    Configure le système de journalisation pour l'application.

    Args:
        log_level_str (str): Niveau de journalisation (DEBUG, INFO, WARNING, ERROR, CRITICAL).
        log_file_path_str (str, optional): Chemin du fichier de journal. 
                                       Si relatif, il sera résolu par rapport à la racine du projet.
    """
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }

    numeric_level = log_levels.get(log_level_str.upper(), logging.INFO)

    # Retirer les handlers existants pour éviter les duplications lors des rechargements (ex: avec uvicorn --reload)
    # Surtout si cette fonction est appelée plusieurs fois.
    # Pour une configuration plus simple au démarrage de l'app, basicConfig est souvent suffisant
    # si appelé une seule fois avant toute autre configuration de logging.
    # Cependant, si on veut ajouter des handlers dynamiquement ou reconfigurer, il faut gérer les handlers existants.
    
    # Au lieu de basicConfig, configurons le root logger manuellement pour plus de contrôle
    # et pour éviter les problèmes si basicConfig a déjà été appelé ailleurs.
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level) # Définir le niveau sur le logger root

    # Supprimer les handlers existants du root logger pour éviter la duplication des logs
    # lors des rechargements de l'application (uvicorn --reload)
    if root_logger.hasHandlers():
        for handler in root_logger.handlers[:]: # Itérer sur une copie de la liste
            root_logger.removeHandler(handler)
            handler.close() # Important pour fermer les fichiers ouverts par les FileHandlers

    # Configurer un handler pour la console (stderr par défaut)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(name)s - [%(filename)s:%(lineno)d] - %(message)s")
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    effective_log_file_path = None
    if log_file_path_str:
        log_file_path = Path(log_file_path_str)
        if not log_file_path.is_absolute():
            effective_log_file_path = PROJECT_ROOT_DIR / log_file_path
        else:
            effective_log_file_path = log_file_path
        
        try:
            log_dir = effective_log_file_path.parent
            if not log_dir.exists():
                log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(effective_log_file_path, encoding='utf-8')
            file_handler.setLevel(numeric_level)
            file_handler.setFormatter(formatter) # Utiliser le même formateur
            root_logger.addHandler(file_handler)
        except Exception as e:
            # Si la création du FileHandler échoue, logguer l'erreur sur la console et continuer sans fichier de log.
            logging.error(f"Impossible de configurer le logging sur fichier {effective_log_file_path}: {e}", exc_info=True)
            effective_log_file_path = None # Réinitialiser pour refléter l'échec

    # Réduire la verbosité de certaines bibliothèques
    logging.getLogger("uvicorn.error").setLevel(logging.INFO) # Uvicorn peut être bruyant en DEBUG
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING) # Logs d'accès peuvent être désactivés ou mis à WARNING
    logging.getLogger("urllib3.connectionpool").setLevel(logging.INFO) # Souvent trop verbeux en DEBUG
    logging.getLogger("httpx").setLevel(logging.INFO) # Si vous utilisez httpx

    logger = logging.getLogger(__name__) # Obtenir un logger pour ce module
    logger.info(f"Logging configuré. Niveau global: {log_level_str.upper()}")
    if effective_log_file_path:
        logger.info(f"Les logs sont également écrits dans le fichier: {effective_log_file_path}")
    else:
        logger.info("Logging sur fichier non activé ou échec de configuration.")

# La fonction get_logger de l'original est redondante car logging.getLogger(name) fait déjà cela.
# Il est préférable d'utiliser directement logging.getLogger(name) dans les modules.
# def get_logger(name: str) -> logging.Logger:
#     """
#     Récupère un logger configuré pour un module spécifique.
#     """
#     return logging.getLogger(name) 