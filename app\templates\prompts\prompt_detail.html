{% extends "base.html" %}
{% import "macros.html" as macros %}

{% block title %}{{ prompt.title }} - PromptAchat{% endblock %}

{% block head %}
{{ super() }}
<script src="{{ url_for('static', path='/js/prompt_detail.js') }}"></script>
{% endblock %}

{% block content %}
{# Header de la page - peut être inclus via un include si utilisé sur plusieurs pages #}
<header class="py-3 mb-4 border-bottom bg-light">
    <div class="container d-flex flex-wrap justify-content-between align-items-center">
        <a href="{{ url_for('root') }}" class="d-flex align-items-center mb-2 mb-lg-0 text-dark text-decoration-none">
            <span class="fs-4">🚀 PromptAchat</span>
        </a>
        <div class="text-end">
            {% if user %}
                <span class="me-3">Utilisateur: {{ user.uid }}</span>
                <form hx-post="{{ url_for('logout') }}" hx-target="body" class="d-inline" hx-headers='{"X-Requested-With": "XMLHttpRequest"}'>
                    {% if request.query_params.get('session_id') %}
                    <input type="hidden" name="session_id" value="{{ request.query_params.get('session_id') }}">
                    {% endif %}
                    <button type="submit" class="btn btn-outline-danger btn-sm">Déconnexion</button>
                </form>
            {% endif %}
        </div>
    </div>
</header>

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>{{ prompt.title }} 
            {% if prompt.is_user_prompt %}
                <span class="badge bg-secondary fs-6 ms-2">Prompt Utilisateur</span>
            {% else %}
                <span class="badge bg-success fs-6 ms-2">Prompt Système</span>
            {% endif %}
        </h2>
        <a href="{{ url_for('root') }}" class="btn btn-sm btn-outline-secondary">Retour à l'accueil</a>
    </div>
    <p class="text-muted">Catégorie : {{ prompt.category_display_name }}</p>

    <hr>

    {# Affichage de la welcome_page_html si présente #}
    {% if prompt.welcome_page_html %}
        <div class="card mb-4">
            <div class="card-header">Instructions initiales</div>
            <div class="card-body">
                {{ prompt.welcome_page_html | safe }}
            </div>
        </div>
    {% endif %}

    <div class="row">
        {# Colonne de Gauche: Configuration et Contenu du Prompt #}
        <div class="col-md-6">
            <h4>Configuration du Prompt</h4>
            
            {# Section Cockpit #}
            {% if prompt.needs_cockpit %}
                <div class="card mb-3">
                    <div class="card-header">Données Cockpit</div>
                    <div class="card-body">
                        <form id="cockpit-form" 
                              hx-post="{{ url_for('retrieve_cockpit_data_for_prompt', prompt_id_for_url=prompt.id_for_url) }}"
                              hx-target="#cockpit-data-summary"
                              hx-swap="innerHTML"
                              hx-headers='{"X-Requested-With": "XMLHttpRequest"}'
                              hx-ext="json-enc">
                            <div class="mb-3">
                                <label for="cockpit_id" class="form-label">ID Cockpit:</label>
                                <input type="text" class="form-control" id="cockpit_id" name="cockpit_id" placeholder="Ex: 12345">
                            </div>
                            {% if request.query_params.get('session_id') %}
                            <input type="hidden" name="session_id" value="{{ request.query_params.get('session_id') }}">
                            {% endif %}
                            <button type="submit" class="btn btn-info btn-sm">Récupérer Données Cockpit</button>
                        </form>
                        <div id="cockpit-data-summary" class="mt-3">
                            {# Le fragment cockpit_summary.html sera injecté ici #}
                        </div>
                    </div>
                </div>
            {% endif %}

            {# Section Fichiers Modifiée #}
            {% if prompt.accepts_files %}
                <div class="card mb-3">
                    <div class="card-header">Fichiers Attachés (PDF)</div>
                    <div class="card-body">
                        <form id="file-upload-form" 
                              hx-post="{{ url_for('upload_files_for_prompt', prompt_id_for_url=prompt.id_for_url) }}" 
                              hx-target="#uploaded-files-list"
                              hx-swap="innerHTML"
                              hx-encoding="multipart/form-data" {# Important pour l'upload de fichiers #}
                              hx-headers='{"X-Requested-With": "XMLHttpRequest"}'
                              hx-indicator="#file-upload-spinner">
                            <div class="mb-3">
                                <label for="pdf_files_input" class="form-label">Sélectionner des fichiers PDF :</label>
                                <input type="file" class="form-control" id="pdf_files_input" name="files" multiple accept=".pdf">
                            </div>
                            {% if request.query_params.get('session_id') %}
                            <input type="hidden" name="session_id" value="{{ request.query_params.get('session_id') }}">
                            {% endif %}
                            <button type="submit" class="btn btn-secondary btn-sm">
                                Téléverser Fichiers
                                <span id="file-upload-spinner" class="htmx-indicator spinner-border spinner-border-sm ms-1" role="status" aria-hidden="true"></span>
                            </button>
                        </form>
                        <div id="uploaded-files-list" class="mt-3">
                            {# Liste des fichiers uploadés ou message d'erreur sera injecté ici #}
                            {# On pourrait pré-remplir avec les fichiers déjà en session si la page est rechargée #}
                            <p class="text-muted small">Aucun fichier téléversé pour ce prompt.</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            {# Section Variables #}
            {% if prompt.variables_definition %}
                <div class="card mb-3">
                    <div class="card-header">Variables du Prompt</div>
                    <div class="card-body" id="prompt-variables-form">
                        <form id="variables-form" data-prompt-id="{{ prompt.id_for_url }}">
                            {% for var_def in prompt.variables_definition %}
                                <div class="mb-3">
                                    <label for="variable_{{ var_def.name }}" class="form-label">{{ var_def.label }} (<code>{{ var_def.name }}</code>)</label>
                                    <input type="text" class="form-control" id="variable_{{ var_def.name }}" name="var_{{ var_def.name }}" placeholder="Valeur pour {{ var_def.label }}">
                                </div>
                            {% endfor %}
                            
                            {% if request.query_params.get('session_id') %}
                            <input type="hidden" name="session_id" value="{{ request.query_params.get('session_id') }}" id="session_id_input">
                            {% endif %}
                            
                            <div class="d-grid">
                                <button type="button" id="preview-prompt-btn" class="btn btn-outline-secondary d-none">
                                    Prévisualiser le prompt
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}

            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#prompt-content-collapse" aria-expanded="false" aria-controls="prompt-content-collapse">
                    <h5 class="mb-0">Contenu du Prompt (Lecture Seule)</h5>
                    <i class="bi bi-chevron-down"></i>
                </div>
                <div class="collapse" id="prompt-content-collapse">
                    <div class="card-body bg-light">
                        <pre style="white-space: pre-wrap; word-wrap: break-word;"><code>{{ prompt.content }}</code></pre>
                    </div>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-header d-flex justify-content-between align-items-center" role="button" data-bs-toggle="collapse" data-bs-target="#preview-result-collapse" aria-expanded="true" aria-controls="preview-result-collapse">
                    <h5 class="mb-0">Prévisualisation / Résultat</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-secondary" id="refresh-view-btn" type="button">
                            <i class="bi bi-arrow-clockwise"></i> Rafraîchir
                        </button>
                        <i class="bi bi-chevron-down ms-2"></i>
                    </div>
                </div>
                <div class="collapse show" id="preview-result-collapse">
                    <div id="prompt-preview-area" class="card-body">
                        {# La prévisualisation sera mise à jour via JS #}
                    </div>
                </div>
            </div>

        </div>

        {# Colonne de Droite: Actions et Zone de Résultat #}
        <div class="col-md-6">
            <h4>Actions</h4>
            <div class="d-grid gap-2 mb-3">
                {% if prompt.category_slug == 'external' %}
                    <button class="btn btn-warning" id="execute-prompt-btn" type="button">Exécuter le Prompt (Externe)</button>
                    <div class="text-muted small">L'exécution utilise l'API GPT-4 externe</div>
                {% elif prompt.category_slug == 'internal' %}
                    <button class="btn btn-success" id="execute-prompt-btn" type="button">Exécuter le Prompt</button>
                    <button class="btn btn-info" id="stream-prompt-btn" type="button">Exécuter en Streaming</button>
                    <button class="btn btn-danger d-none" id="stop-stream-btn" type="button">Arrêter le Streaming</button>
                {% endif %}
            </div>

            <div class="card mb-3">
                <div class="card-header">Réponse du LLM</div>
                <div id="llm-response-area" class="card-body" style="min-height: 200px; background-color: #e9ecef;">
                    {# La réponse du LLM apparaîtra ici #}
                    <em class="text-muted">La réponse du LLM apparaîtra ici...</em>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %} 