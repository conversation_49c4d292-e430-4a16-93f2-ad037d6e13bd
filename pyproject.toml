[tool.poetry]
name = "promptachat-fastapi"
version = "0.1.0"
description = "Portage de PromptAchat en FastAPI"
authors = ["Votre Nom <<EMAIL>>"] # Adaptez ceci
readme = "README_fastapi.md"

[tool.poetry.dependencies]
python = "^3.9" # Assurez-vous que cela correspond à votre environnement
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.23.2"}
jinja2 = "^3.1.2"
python-multipart = "^0.0.6"
pydantic = "^2.4.2"
openai = "^0.28.1" # Vérifiez la dernière version compatible si besoin
pypdf2 = "^3.0.1"
pyperclip = "^1.8.2"
ldap3 = "^2.9.1"
requests = "^2.31.0"
# markdown = "^3.5" # Décommenter si vous souhaitez l'ajouter

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api" 