# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
config.ini
promptachat-streamlit
t

# C extensions
*.so

# Distribution / packaging
.Python
build/
dist/
downloads/
eggs/
.eggs/
lib/
lib60/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a PyInstaller script
#  *.spec
#  PyInstaller dist/
#  PyInstaller build/

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*_fastapi
.cache
osetests.xml
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# PEP 582; __pypackages__ directory
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath files
*.sage.py

# Environments
.env
.venv
venv/
ENV/
env/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDEs and editors
.idea/
.vscode/*_fastapi
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.sublime-project
*.sublime-workspace

# SQLite
*.db
*.db-journal

# Config files not to be versioned
config.ini # Si vous ne voulez pas versionner les clés API directement

# Poetry
poetry.lock # Peut être versionné ou non selon la politique de l'équipe

# PDM
pdm.lock # Peut être versionné ou non selon la politique de l'équipe
.pdm.toml 